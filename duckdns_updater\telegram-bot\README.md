# Arr-Bot 🤖

Bot de Telegram para gestionar Radarr y Sonarr, integrado con el stack DNS DuckDNS.

## 🚀 Características

### Funcionalidades Principales

- **🔄 Manejo Automático de Fallos de Captura**: Recibe notificaciones cuando Radarr/Sonarr fallan al capturar contenido y ofrece lanzamientos alternativos
- **➕ Añadir Contenido Remotamente**: Busca y añade películas/series desde Telegram
- **📊 Estado del Sistema**: Monitorea el estado de Radarr y Sonarr en tiempo real

### Comandos Disponibles

- `/start` - Iniciar el bot y ver información
- `/agregar [término]` - Buscar y añadir contenido
- `/estado` - Ver estado de los servicios
- `/ayuda` - Mostrar ayuda

## 🛠️ Instalación y Configuración

### Prerrequisitos

- Docker y Docker Compose
- Bot de Telegram creado con @BotFather
- Radarr y Sonarr funcionando
- Stack DNS DuckDNS configurado

### 1. Configuración del Bot de Telegram

1. Habla con [@BotFather](https://t.me/BotFather) en Telegram
2. Crea un nuevo bot con `/newbot`
3. Guarda el token proporcionado
4. Obtén tu Chat ID enviando un mensaje a tu bot y visitando:
   ```
   https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates
   ```

### 2. Configuración de Variables de Entorno

1. Copia el archivo de ejemplo:
   ```bash
   cp .env.example .env
   ```

2. Edita `.env` con tus valores:
   ```env
   TELEGRAM_BOT_TOKEN=tu_token_aqui
   TELEGRAM_CHAT_ID=tu_chat_id_aqui
   RADARR_API_KEY=tu_api_key_radarr
   SONARR_API_KEY=tu_api_key_sonarr
   ```

### 3. Obtener API Keys

#### Radarr:
1. Ve a Configuración > General
2. Copia la "API Key"

#### Sonarr:
1. Ve a Configuración > General
2. Copia la "API Key"

### 4. Configurar Webhooks

#### En Radarr:
1. Ve a Configuración > Conexiones
2. Añade una nueva conexión "Webhook"
3. Configura:
   - **URL**: `http://arr-bot:8082/webhook/radarr`
   - **Método**: POST
   - **Triggers**: Marca "On Grab"

#### En Sonarr:
1. Ve a Configuración > Conexiones
2. Añade una nueva conexión "Webhook"
3. Configura:
   - **URL**: `http://arr-bot:8082/webhook/sonarr`
   - **Método**: POST
   - **Triggers**: Marca "On Grab"

## 🐳 Integración con Docker Compose

El bot está diseñado para integrarse con el stack DNS existente. Se añadirá automáticamente al archivo `DNS-compose.yml`.

### Estructura de Red

- **Red**: `caddy_network`
- **Puerto**: 8082 (para evitar conflictos con AdGuard)
- **Comunicación**: Usa `host.docker.internal` para acceder a servicios del host

## 📝 Uso

### Manejo de Fallos de Captura

Cuando Radarr o Sonarr fallan al capturar contenido:

1. Recibirás una notificación automática en Telegram
2. El bot mostrará lanzamientos alternativos disponibles
3. Selecciona una alternativa para iniciar la descarga
4. Recibirás confirmación del resultado

### Añadir Contenido

```
/agregar The Matrix
```

1. El bot buscará en Radarr y Sonarr
2. Mostrará resultados con información detallada
3. Selecciona el contenido a añadir
4. Se añadirá automáticamente a la biblioteca

### Verificar Estado

```
/estado
```

Muestra:
- Estado de conexión de Radarr/Sonarr
- Versiones de los servicios
- Elementos en cola
- Espacio en disco disponible

## 🔧 Troubleshooting

### Problemas Comunes

#### Bot no responde
- Verifica que el token de Telegram sea correcto
- Comprueba que el Chat ID sea válido
- Revisa los logs del contenedor

#### No se conecta a Radarr/Sonarr
- Verifica las API Keys
- Comprueba que las URLs sean correctas
- Asegúrate de que los servicios estén accesibles desde el contenedor

#### Webhooks no funcionan
- Verifica que las URLs de webhook sean correctas
- Comprueba que el puerto 8082 esté expuesto
- Revisa la configuración de red en Docker

### Logs

Para ver los logs del bot:
```bash
docker logs arr-bot
```

## 🌐 Integración con Stack DNS

Este bot está específicamente diseñado para integrarse con el stack DNS DuckDNS existente:

- Utiliza la red `caddy_network` para comunicación
- Puerto 8082 para evitar conflictos con AdGuard (8080)
- Configurado para acceder a servicios del host via `host.docker.internal`
- Integración automática con Watchtower para actualizaciones

## 📄 Licencia

Este proyecto es de código abierto y está disponible bajo la licencia MIT.
