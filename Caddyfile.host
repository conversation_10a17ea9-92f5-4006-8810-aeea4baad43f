# Caddyfile - Configuración optimizada para Media Server
# Versión adaptada para Docker manteniendo servicios en el host
# Basado en tu configuración original con rutas de logs adaptadas

# === CONFIGURACIÓN GLOBAL ===
{
	# Configuración global de seguridad y rendimiento
	servers {
		timeouts {
			read_body 30s
			read_header 10s
			write 30s
			idle 120s
		}
	}
}

# === JELLYFIN MEDIA SERVER ===
tankeflix.duckdns.org {
	# Compresión para mejor rendimiento
	encode {
		gzip 6
		zstd
	}

	# Configuración de seguridad y rendimiento

	# Proxy inverso hacia Jellyfin con configuración optimizada
	reverse_proxy host.docker.internal:8096 {
		# Headers esenciales para Jellyfin
		header_up Host {upstream_hostport}
		header_up X-Real-IP {remote_host}

		# Timeouts específicos para streaming
		transport http {
			read_timeout 60s
			write_timeout 60s
			dial_timeout 10s
		}
	}

	# Headers de seguridad optimizados para Jellyfin
	header {
		# Seguridad básica
		X-Content-Type-Options "nosniff"
		X-Frame-Options "SAMEORIGIN" # Cambiado de DENY para permitir embeds de Jellyfin
		Referrer-Policy "strict-origin-when-cross-origin"

		# HSTS con configuración robusta
		Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

		# CSP relajado para Jellyfin (necesita inline scripts y estilos)
		Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: *; media-src 'self' blob: data: *"

		# Headers adicionales de seguridad
		Permissions-Policy "geolocation=(), microphone=(), camera=()"
		X-Permitted-Cross-Domain-Policies "none"

		# Ocultar información del servidor
		-Server
	}

	# Logging adaptado para Docker (mantiene estructura de directorios)
	log {
		output file /var/log/caddy/Jellyfin/jellyfin.log {
			roll_size 50MiB
			roll_keep 10
			roll_keep_for 720h # 30 días
		}
		format json
		level INFO
	}

	# Manejo de errores personalizado
	handle_errors {
		@5xx expression {http.error.status_code} >= 500
		respond @5xx "Servicio temporalmente no disponible" 503

		@4xx expression {http.error.status_code} >= 400
		respond @4xx "Recurso no encontrado" 404
	}

	# TLS con configuración segura
	tls <EMAIL> {
		protocols tls1.2 tls1.3
	}
}

# === QBITTORRENT TORRENT CLIENT ===
tanketorrent.duckdns.org {
	encode zstd gzip

	reverse_proxy host.docker.internal:8091 {
		header_up Host {host}
		header_up X-Real-IP {remote_ip}
		transport http {
			read_timeout 30s
			write_timeout 30s
			dial_timeout 5s
		}
	}

	header {
		X-Content-Type-Options "nosniff"
		
		# --- LA CORRECCIÓN ESTÁ AQUÍ ---
		# Cambiado de DENY a SAMEORIGIN para permitir que la ventana de subida funcione.
		X-Frame-Options "SAMEORIGIN"

		Referrer-Policy "strict-origin-when-cross-origin"
		Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
		Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
		Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=()"
		X-Permitted-Cross-Domain-Policies "none"
		-Server
	}

	# ... el resto de tu configuración de log, handle_errors y tls va aquí ...
    # Déjalo como estaba.
	log {
		output file /var/log/caddy/Qbittorrent/qbittorrent.log {
			roll_size 25MiB
			roll_keep 8
			roll_keep_for 480h # 20 días
		}
		format json
		level INFO
	}

	handle_errors {
		@5xx expression {http.error.status_code} >= 500
		respond @5xx "qBittorrent temporalmente no disponible" 503

		@4xx expression {http.error.status_code} >= 400
		respond @4xx "Acceso no autorizado" 401
	}

	tls <EMAIL> {
		protocols tls1.2 tls1.3
	}
}


# === SNIPPET REUTILIZABLE PARA APLICACIONES *ARR ===
(arr_common) {
	# Compresión para interfaces web
	encode {
		gzip 6
		zstd
	}

	# Configuración de seguridad para aplicaciones *arr

	# Headers de seguridad para aplicaciones *arr
	header {
		X-Content-Type-Options "nosniff"
		X-Frame-Options "SAMEORIGIN" # Permite embeds para notificaciones
		Referrer-Policy "strict-origin-when-cross-origin"
		Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

		# CSP balanceado para funcionalidad completa
		Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: *"

		Permissions-Policy "geolocation=(), microphone=(), camera=()"
		X-Permitted-Cross-Domain-Policies "none"
		-Server
	}

	# Manejo de errores consistente
	handle_errors {
		@5xx expression {http.error.status_code} >= 500
		respond @5xx "{args[1]} temporalmente no disponible" 503

		@4xx expression {http.error.status_code} >= 400
		respond @4xx "Recurso no encontrado en {args[1]}" 404
	}

	# TLS seguro
	tls <EMAIL> {
		protocols tls1.2 tls1.3
	}
}

# === APLICACIONES *ARR CON DOMINIOS SEPARADOS ===

# Sonarr - Gestión de Series
tankesonarr.duckdns.org {
	# Importar configuración común
	import arr_common sonarr "Sonarr"

	# Proxy específico para Sonarr
	reverse_proxy host.docker.internal:8989 {
		header_up Host {upstream_hostport}
		header_up X-Real-IP {remote_host}

		# Timeouts optimizados para operaciones de series
		transport http {
			read_timeout 45s
			write_timeout 45s
			dial_timeout 5s
		}
	}

	# Logging específico para Sonarr
	log {
		output file /var/log/caddy/Sonarr/sonarr.log {
			roll_size 30MiB
			roll_keep 8
			roll_keep_for 480h # 20 días
		}
		format json
		level INFO
	}
}

# Jackett - Indexador de Torrents
tankejackett.duckdns.org {
	# Importar configuración común
	import arr_common jackett "Jackett"

	# Proxy específico para Jackett
	reverse_proxy host.docker.internal:9117 {
		header_up Host {upstream_hostport}
		header_up X-Real-IP {remote_host}

		# Timeouts para búsquedas de indexadores
		transport http {
			read_timeout 60s # Búsquedas pueden tomar tiempo
			write_timeout 30s
			dial_timeout 5s
		}
	}

	# Logging específico para Jackett
	log {
		output file /var/log/caddy/Jackett/jackett.log {
			roll_size 25MiB
			roll_keep 6
			roll_keep_for 360h # 15 días
		}
		format json
		level INFO
	}
}


# Radarr - Gestión de Películas
tankeradarr.duckdns.org {
	# Importar configuración común
	import arr_common radarr "Radarr"

	# Proxy específico para Radarr
	reverse_proxy host.docker.internal:7878 {
		header_up Host {upstream_hostport}
		header_up X-Real-IP {remote_host}

		# Timeouts optimizados para operaciones de películas
		transport http {
			read_timeout 45s
			write_timeout 45s
			dial_timeout 5s
		}
	}

	# Logging específico para Radarr
	log {
		output file /var/log/caddy/Radarr/radarr.log {
			roll_size 30MiB
			roll_keep 8
			roll_keep_for 480h # 20 días
		}
		format json
		level INFO
	}
}

# === JELLYSEERR REQUEST MANAGEMENT ===
tankejellyseerr.duckdns.org {
	# Importar configuración común
	import arr_common jellyseerr "Jellyseerr"

	# Proxy específico para Jellyseerr
	reverse_proxy host.docker.internal:5055 {
		header_up Host {upstream_hostport}
		header_up X-Real-IP {remote_host}

		# Timeouts optimizados para Jellyseerr
		transport http {
			read_timeout 45s
			write_timeout 45s
			dial_timeout 5s
		}
	}

	# Logging específico para Jellyseerr
	log {
		output file /var/log/caddy/Jellyseerr/jellyseerr.log {
			roll_size 30MiB
			roll_keep 8
			roll_keep_for 480h # 20 días
		}
		format json
		level INFO
	}
}

# === ADGUARD HOME DNS SERVER & AD BLOCKER ===
tankeguard.duckdns.org {
	# Compresión optimizada para AdGuard Home
	encode {
		gzip 6
		zstd
	}

	# Proxy específico para AdGuard Home
	reverse_proxy host.docker.internal:8080 {
		# Headers críticos para AdGuard Home
		header_up Host {host}
		header_up X-Real-IP {remote_host}
		header_up X-Forwarded-Proto {scheme}

		# Timeouts específicos para AdGuard Home
		transport http {
			read_timeout 60s
			write_timeout 60s
			dial_timeout 10s
		}
	}

	# Headers de seguridad específicos para AdGuard Home
	header {
		X-Content-Type-Options "nosniff"
		X-Frame-Options "SAMEORIGIN"  # Permite embeds para gráficos y estadísticas
		Referrer-Policy "strict-origin-when-cross-origin"
		Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

		# CSP optimizado para AdGuard Home (interfaz web con gráficos)
		Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *"

		# Ocultar información del servidor
		-Server
	}

	# Logging específico para AdGuard Home
	log {
		output file /var/log/caddy/AdGuard/adguard.log {
			roll_size 30MiB
			roll_keep 8
			roll_keep_for 480h # 20 días
		}
		format json
		level INFO
	}

	# Manejo de errores específico para AdGuard Home
	handle_errors {
		@5xx expression {http.error.status_code} >= 500
		respond @5xx "AdGuard Home temporalmente no disponible" 503

		@4xx expression {http.error.status_code} >= 400
		respond @4xx "Acceso no autorizado a AdGuard Home" 401
	}

	# Ruta especial para DNS-over-HTTPS
	handle /dns-query {
		reverse_proxy host.docker.internal:8080 {
			header_up Host {host}
			header_up X-Real-IP {remote_host}
		}
	}

	# TLS seguro
	tls <EMAIL> {
		protocols tls1.2 tls1.3
	}
}

# === HOMEPAGE DASHBOARD ===
tankeeee2.duckdns.org {
	# Compresión optimizada para Homepage
	encode {
		gzip 6
		zstd
	}

	# Proxy específico para Homepage - Puerto corregido
	reverse_proxy host.docker.internal:3001 {
		header_up Host {upstream_hostport}
		header_up X-Real-IP {remote_host}
		header_up X-Forwarded-For {remote_host}
		header_up X-Forwarded-Proto {scheme}

		# Timeouts específicos para Homepage
		transport http {
			read_timeout 60s
			write_timeout 60s
			dial_timeout 10s
		}
	}

	# Headers de seguridad específicos para Homepage
	header {
		X-Content-Type-Options "nosniff"
		X-Frame-Options "SAMEORIGIN"  # Homepage necesita frames para widgets y servicios
		Referrer-Policy "strict-origin-when-cross-origin"
		Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"

		# CSP optimizado para Homepage (dashboard con widgets, APIs y servicios externos)
		Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: *; script-src 'self' 'unsafe-inline' 'unsafe-eval' *; style-src 'self' 'unsafe-inline' *; img-src 'self' data: blob: *; connect-src 'self' *; font-src 'self' data: *; frame-src 'self' *"

		# Ocultar información del servidor
		-Server
	}

	# Logging específico para Homepage
	log {
		output file /var/log/caddy/Homepage/homepage.log {
			roll_size 30MiB
			roll_keep 8
			roll_keep_for 480h # 20 días
		}
		format json
		level INFO
	}

	# Manejo de errores específico para Homepage
	handle_errors {
		@5xx expression {http.error.status_code} >= 500
		respond @5xx "Homepage temporalmente no disponible" 503

		@4xx expression {http.error.status_code} >= 400
		respond @4xx "Acceso no autorizado a Homepage" 401
	}

	# TLS seguro
	tls <EMAIL> {
		protocols tls1.2 tls1.3
	}
}

# === PORTAINER DOCKER MANAGEMENT ===
tankeportainer.duckdns.org {
	# Compresión optimizada para Portainer
	encode {
		gzip 6
		zstd
	}

	# Proxy optimizado para Portainer con acceso remoto
	reverse_proxy host.docker.internal:9000 {
		# Headers básicos para proxy reverso
		header_up Host "host.docker.internal:9000"
		header_up X-Real-IP {remote_host}
		header_up X-Forwarded-For {remote_host}
		header_up X-Forwarded-Proto "http"
		header_up X-Forwarded-Host "host.docker.internal:9000"

		# Headers específicos para evitar CSRF
		header_up Origin "http://host.docker.internal:9000"
		header_up Referer "http://host.docker.internal:9000/"

		# Timeouts específicos para Portainer
		transport http {
			read_timeout 120s  # Aumentado para operaciones Docker largas
			write_timeout 120s
			dial_timeout 15s
		}
	}

	# Headers simplificados y optimizados para Portainer
	header {
		# Headers básicos de seguridad
		X-Content-Type-Options "nosniff"
		X-Frame-Options "SAMEORIGIN"

		# CSP muy permisivo para Portainer
		Content-Security-Policy "default-src * 'unsafe-inline' 'unsafe-eval' data: blob:; script-src * 'unsafe-inline' 'unsafe-eval'; connect-src * ws: wss:;"

		# CORS completamente abierto para Portainer
		Access-Control-Allow-Origin "*"
		Access-Control-Allow-Methods "*"
		Access-Control-Allow-Headers "*"
		Access-Control-Allow-Credentials "true"

		# Remover headers problemáticos
		-Server
		-Strict-Transport-Security
	}

	# Logging específico para Portainer
	log {
		output file /var/log/caddy/Portainer/portainer.log {
			roll_size 25MiB
			roll_keep 6
			roll_keep_for 360h # 15 días
		}
		format json
		level INFO
	}

	# Manejo de errores específico para Portainer
	handle_errors {
		@5xx expression {http.error.status_code} >= 500
		respond @5xx "Portainer temporalmente no disponible" 503

		@4xx expression {http.error.status_code} >= 400
		respond @4xx "Acceso no autorizado a Portainer" 401
	}

	# TLS seguro
	tls <EMAIL> {
		protocols tls1.2 tls1.3
	}
}

