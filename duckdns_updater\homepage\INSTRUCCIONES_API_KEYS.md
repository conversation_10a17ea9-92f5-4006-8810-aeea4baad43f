# 🔑 Guía para Obtener API Keys y Credenciales

Esta guía te ayudará a obtener todas las API keys y credenciales necesarias para que los widgets de Homepage funcionen correctamente.

## 📋 Lista de Credenciales Necesarias

### ✅ Ya Configuradas (encontradas en tu sistema)
- **Sonarr API Key**: `2f9da07e98744f4890c0960d15ead111`
- **Radarr API Key**: `cfc0cde90b0f483eb4190dc634ca86f2`
- **Jellyfin API Key**: `6a794b57ff4d4d10bf31875612761d8e`
- **Jackett API Key**: `xb68zjzfmw3cnbbmufcxr9ou7kh8te37`
- **Jellyseerr API Key**: `MTc1Mjc2MDk0NTQ5MzIzMjNhMzVmLWRlOTktNDkxMi04Y2IyLTYzNDU4OTFiN2U5Yg==`
- **AdGuard Username**: `Tankeeee2_GAMES`

### ❌ Pendientes de Configurar

#### 1. **AdGuard Home Password**
- **Ubicación**: `HOMEPAGE_VAR_ADGUARD_PASSWORD`
- **Cómo obtenerla**: Es la contraseña que configuraste para el usuario `Tankeeee2_GAMES` en AdGuard Home
- **Dónde cambiarla**: Archivo `docker.secrets.yaml`, línea 7

#### 2. **Portainer API Key**
- **Ubicación**: `HOMEPAGE_VAR_PORTAINER_API_KEY`
- **Cómo obtenerla**:
  1. Ve a http://tankeportainer.duckdns.org
  2. Inicia sesión con tu usuario
  3. Ve a **User settings** (icono de usuario arriba a la derecha)
  4. Selecciona **Access tokens**
  5. Haz clic en **Add access token**
  6. Dale un nombre (ej: "Homepage Widget")
  7. Copia el token generado
- **Dónde cambiarla**: Archivo `docker.secrets.yaml`, línea 11

#### 3. **qBittorrent Credentials**
- **Ubicación**: `HOMEPAGE_VAR_QBITTORRENT_USERNAME` y `HOMEPAGE_VAR_QBITTORRENT_PASSWORD`
- **Cómo obtenerlas**:
  1. Ve a http://tanketorrent.duckdns.org
  2. Usa las credenciales que configuraste para qBittorrent
  3. Si no las recuerdas, puedes cambiarlas en la interfaz web:
     - Ve a **Tools** > **Options** > **Web UI**
     - Cambia el username y password
- **Dónde cambiarlas**: Archivo `docker.secrets.yaml`, líneas 14-15

## 🔧 Pasos para Actualizar las Credenciales

### 1. Editar el archivo de secrets
```bash
# Navega al directorio de Homepage
cd C:\docker\duckdns_updater\homepage\config

# Edita el archivo docker.secrets.yaml
notepad docker.secrets.yaml
```

### 2. Reemplazar los valores marcados con "TU_"
Busca estas líneas y reemplaza los valores:

```yaml
# Reemplaza con tu contraseña real de AdGuard
HOMEPAGE_VAR_ADGUARD_PASSWORD: "TU_CONTRASEÑA_ADGUARD"

# Reemplaza con el API key de Portainer
HOMEPAGE_VAR_PORTAINER_API_KEY: "TU_API_KEY_DE_PORTAINER"

# Reemplaza con tus credenciales de qBittorrent
HOMEPAGE_VAR_QBITTORRENT_USERNAME: "admin"
HOMEPAGE_VAR_QBITTORRENT_PASSWORD: "TU_CONTRASEÑA_QBITTORRENT"
```

### 3. Reiniciar Homepage
```bash
# Reinicia el contenedor para aplicar los cambios
docker-compose -f duckdns_updater/DNS-compose.yml restart homepage
```

## 🎯 Verificación de Funcionamiento

Después de configurar todas las credenciales:

1. **Ve a**: https://tankeeee2.duckdns.org
2. **Verifica que aparezcan**:
   - Estadísticas de AdGuard Home (consultas DNS, bloqueos)
   - Estado de contenedores en Portainer
   - Estadísticas de descargas en qBittorrent
   - Información de series/películas en Sonarr/Radarr
   - Estadísticas de biblioteca en Jellyfin
   - Estado de indexers en Jackett

## 🚨 Solución de Problemas

### Si un widget no funciona:
1. **Verifica la API key**: Asegúrate de que esté correcta en `docker.secrets.yaml`
2. **Verifica la URL**: Confirma que el servicio esté accesible
3. **Revisa los logs**: `docker logs homepage`
4. **Reinicia el contenedor**: `docker restart homepage`

### Si aparece "Error de conexión":
- Verifica que el servicio esté ejecutándose
- Confirma que la URL sea correcta
- Asegúrate de que no haya problemas de red

## 📝 Notas Importantes

- **Seguridad**: Nunca compartas tus API keys públicamente
- **Backup**: Guarda una copia de seguridad de `docker.secrets.yaml`
- **Actualizaciones**: Si cambias credenciales en los servicios, actualiza también el archivo de secrets
- **Permisos**: El contenedor de Homepage necesita acceso al socket de Docker para mostrar estadísticas de contenedores
