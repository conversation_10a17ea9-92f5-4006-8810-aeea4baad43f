[2025-07-29T10:21:40.187Z] info: kubernetes.yaml was copied to the config folder
[2025-07-29T10:23:53.933Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:53.953Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:53.968Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:55.428Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:55.531Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:55.553Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:55.567Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:55.583Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:55.589Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:55.599Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:55.606Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:55.610Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:55.622Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:55.628Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:55.651Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.143Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.183Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.197Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.210Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.215Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.222Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.228Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.242Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.244Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.247Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.249Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.257Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.270Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.593Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.622Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.625Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.629Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.632Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.638Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.644Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.652Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.656Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.663Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.669Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.678Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.703Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.758Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.796Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.800Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.807Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.812Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.819Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.828Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.833Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.838Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.843Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.853Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.861Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.864Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.925Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.957Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.960Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.964Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.968Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.971Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.979Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.984Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.987Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.990Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:58.995Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:59.005Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:59.014Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:59.060Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:59.088Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:59.092Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:59.096Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:59.103Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:59.107Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:59.118Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:59.131Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:59.136Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:59.140Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:59.142Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:59.147Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:23:59.175Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:24:05.550Z] info: kubernetes.yaml was copied to the config folder
[2025-07-29T10:32:48.788Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:48.956Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:48.968Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:48.979Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:48.999Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:49.013Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:49.017Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:49.023Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:49.043Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:49.055Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:49.066Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:49.076Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:49.081Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:51.152Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:51.292Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:51.307Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:51.317Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:51.330Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:51.356Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:51.366Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:51.377Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:51.384Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:51.390Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:51.419Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:51.429Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:51.436Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:32:59.929Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.041Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.054Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.072Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.080Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.090Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.108Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.126Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.135Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.150Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.188Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.205Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.233Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.676Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.711Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.718Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.724Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.731Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.738Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.747Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.751Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.755Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.759Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.766Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.773Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:00.793Z] error: Host validation failed for: *************:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:01.366Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:01.368Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:01.375Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:01.659Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:01.663Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:01.667Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:05.864Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:25.676Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:25.679Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:25.687Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:33:27.292Z] error: Host validation failed for: host.docker.internal:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:34:12.086Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:34:12.097Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:34:12.102Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:34:35.622Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:34:35.631Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:36:08.871Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:36:08.995Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:36:09.007Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:36:09.020Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:36:09.025Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:36:09.032Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:36:09.038Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:36:09.044Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:36:09.052Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:36:09.061Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:36:09.065Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:36:09.068Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:36:09.083Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:36:10.014Z] error: Host validation failed for: localhost:3001. Hint: Set the HOMEPAGE_ALLOWED_HOSTS environment variable to allow requests from this host / port.
[2025-07-29T10:37:40.811Z] info: docker.yaml was copied to the config folder
[2025-07-29T10:37:40.835Z] info: proxmox.yaml was copied to the config folder
[2025-07-29T10:37:40.926Z] info: custom.css was copied to the config folder
[2025-07-29T10:37:40.931Z] info: custom.js was copied to the config folder
[2025-07-29T10:37:41.177Z] error: Failed to load widgets, please check widgets.yaml for errors or remove example entries.
[2025-07-29T10:37:41.178Z] error: bad indentation of a mapping entry (49:26)

 46 |     - storage_f:
 47 |         type: storage
 48 |         path: /mnt/f
 49 |         label: "Disco F:"```
-------------------------------^
 50 | 
 51 | #### **4. `bookmarks.yaml`**
[2025-07-29T10:37:41.331Z] error: Failed to load widgets, please check widgets.yaml for errors or remove example entries.
[2025-07-29T10:37:41.332Z] error: bad indentation of a mapping entry (49:26)

 46 |     - storage_f:
 47 |         type: storage
 48 |         path: /mnt/f
 49 |         label: "Disco F:"```
-------------------------------^
 50 | 
 51 | #### **4. `bookmarks.yaml`**
[2025-07-29T10:37:41.632Z] error: <dockerStatusService> Error: connect EACCES /var/run/docker.sock
    at PipeConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)
    at PipeConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17)
[2025-07-29T10:37:41.639Z] error: <dockerStatsService> Error: connect EACCES /var/run/docker.sock
    at PipeConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)
    at PipeConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17)
[2025-07-29T10:37:41.652Z] error: Failed to load widgets, please check widgets.yaml for errors or remove example entries.
[2025-07-29T10:37:41.652Z] error: bad indentation of a mapping entry (49:26)

 46 |     - storage_f:
 47 |         type: storage
 48 |         path: /mnt/f
 49 |         label: "Disco F:"```
-------------------------------^
 50 | 
 51 | #### **4. `bookmarks.yaml`**
[2025-07-29T10:38:14.314Z] error: <dockerStatusService> Error: connect EACCES /var/run/docker.sock
    at PipeConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)
    at PipeConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17)
[2025-07-29T10:38:14.339Z] error: <dockerStatsService> Error: connect EACCES /var/run/docker.sock
    at PipeConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)
    at PipeConnectWrap.callbackTrampoline (node:internal/async_hooks:130:17)
[2025-07-29T10:38:14.343Z] error: Failed to load widgets, please check widgets.yaml for errors or remove example entries.
[2025-07-29T10:38:14.343Z] error: bad indentation of a mapping entry (49:26)

 46 |     - storage_f:
 47 |         type: storage
 48 |         path: /mnt/f
 49 |         label: "Disco F:"```
-------------------------------^
 50 | 
 51 | #### **4. `bookmarks.yaml`**
