# configurar_homepage.ps1 - Script para configurar Homepage automáticamente
# Ejecuta este script para configurar todas las credenciales de Homepage

Write-Host "🏠 CONFIGURADOR DE HOMEPAGE TANKEEEE2" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""

# Verificar que estamos en el directorio correcto
$currentPath = Get-Location
if (-not (Test-Path "config\docker.secrets.yaml")) {
    Write-Host "❌ Error: No se encuentra el archivo docker.secrets.yaml" -ForegroundColor Red
    Write-Host "   Asegúrate de ejecutar este script desde el directorio homepage" -ForegroundColor Yellow
    Write-Host "   Directorio actual: $currentPath" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Archivo de configuración encontrado" -ForegroundColor Green
Write-Host ""

# Leer el archivo actual
$secretsFile = "config\docker.secrets.yaml"
$content = Get-Content $secretsFile -Raw

Write-Host "🔑 CONFIGURACIÓN DE CREDENCIALES" -ForegroundColor Yellow
Write-Host "================================" -ForegroundColor Yellow
Write-Host ""

# AdGuard Home Password
Write-Host "1. ADGUARD HOME" -ForegroundColor Cyan
Write-Host "   Usuario actual: Tankeeee2_GAMES" -ForegroundColor Gray
$adguardPassword = Read-Host "   Ingresa la contraseña de AdGuard Home"
if ($adguardPassword) {
    $content = $content -replace 'HOMEPAGE_VAR_ADGUARD_PASSWORD: "TU_CONTRASEÑA_ADGUARD"', "HOMEPAGE_VAR_ADGUARD_PASSWORD: `"$adguardPassword`""
    Write-Host "   ✅ Contraseña de AdGuard configurada" -ForegroundColor Green
}
Write-Host ""

# Portainer API Key
Write-Host "2. PORTAINER" -ForegroundColor Cyan
Write-Host "   Para obtener la API Key:" -ForegroundColor Gray
Write-Host "   - Ve a http://tankeportainer.duckdns.org" -ForegroundColor Gray
Write-Host "   - User settings > Access tokens > Add access token" -ForegroundColor Gray
$portainerKey = Read-Host "   Ingresa la API Key de Portainer"
if ($portainerKey) {
    $content = $content -replace 'HOMEPAGE_VAR_PORTAINER_API_KEY: "TU_API_KEY_DE_PORTAINER"', "HOMEPAGE_VAR_PORTAINER_API_KEY: `"$portainerKey`""
    Write-Host "   ✅ API Key de Portainer configurada" -ForegroundColor Green
}
Write-Host ""

# qBittorrent Credentials
Write-Host "3. QBITTORRENT" -ForegroundColor Cyan
$qbitUser = Read-Host "   Ingresa el usuario de qBittorrent (por defecto: admin)"
if (-not $qbitUser) { $qbitUser = "admin" }
$qbitPassword = Read-Host "   Ingresa la contraseña de qBittorrent"
if ($qbitPassword) {
    $content = $content -replace 'HOMEPAGE_VAR_QBITTORRENT_USERNAME: "admin"', "HOMEPAGE_VAR_QBITTORRENT_USERNAME: `"$qbitUser`""
    $content = $content -replace 'HOMEPAGE_VAR_QBITTORRENT_PASSWORD: "TU_CONTRASEÑA_QBITTORRENT"', "HOMEPAGE_VAR_QBITTORRENT_PASSWORD: `"$qbitPassword`""
    Write-Host "   ✅ Credenciales de qBittorrent configuradas" -ForegroundColor Green
}
Write-Host ""

# Verificar si hay más API keys que configurar
Write-Host "4. VERIFICACIÓN DE API KEYS EXISTENTES" -ForegroundColor Cyan
Write-Host "   Las siguientes API keys ya están configuradas:" -ForegroundColor Gray
Write-Host "   ✅ Sonarr API Key" -ForegroundColor Green
Write-Host "   ✅ Radarr API Key" -ForegroundColor Green
Write-Host "   ✅ Jellyfin API Key" -ForegroundColor Green
Write-Host "   ✅ Jackett API Key" -ForegroundColor Green
Write-Host "   ✅ Jellyseerr API Key" -ForegroundColor Green
Write-Host ""

# Guardar el archivo actualizado
try {
    $content | Set-Content $secretsFile -Encoding UTF8
    Write-Host "💾 CONFIGURACIÓN GUARDADA" -ForegroundColor Green
    Write-Host "=========================" -ForegroundColor Green
    Write-Host "   Archivo actualizado: $secretsFile" -ForegroundColor Gray
    Write-Host ""
} catch {
    Write-Host "❌ Error al guardar el archivo: $_" -ForegroundColor Red
    exit 1
}

# Reiniciar Homepage
Write-Host "🔄 REINICIANDO HOMEPAGE" -ForegroundColor Yellow
Write-Host "=======================" -ForegroundColor Yellow
Write-Host ""

try {
    # Cambiar al directorio raíz de Docker
    Set-Location "..\.."
    
    Write-Host "   Reiniciando contenedor de Homepage..." -ForegroundColor Gray
    $result = docker-compose -f duckdns_updater/DNS-compose.yml restart homepage 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✅ Homepage reiniciado correctamente" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  Advertencia: Error al reiniciar Homepage" -ForegroundColor Yellow
        Write-Host "   Puedes reiniciarlo manualmente con:" -ForegroundColor Gray
        Write-Host "   docker-compose -f duckdns_updater/DNS-compose.yml restart homepage" -ForegroundColor Gray
    }
} catch {
    Write-Host "   ⚠️  No se pudo reiniciar automáticamente" -ForegroundColor Yellow
    Write-Host "   Reinicia manualmente con:" -ForegroundColor Gray
    Write-Host "   docker-compose -f duckdns_updater/DNS-compose.yml restart homepage" -ForegroundColor Gray
}

Write-Host ""
Write-Host "🎉 CONFIGURACIÓN COMPLETADA" -ForegroundColor Green
Write-Host "============================" -ForegroundColor Green
Write-Host ""
Write-Host "📋 PRÓXIMOS PASOS:" -ForegroundColor Cyan
Write-Host "   1. Ve a https://tankeeee2.duckdns.org" -ForegroundColor White
Write-Host "   2. Verifica que todos los widgets muestren datos" -ForegroundColor White
Write-Host "   3. Si algún widget no funciona, revisa las credenciales" -ForegroundColor White
Write-Host ""
Write-Host "📚 DOCUMENTACIÓN:" -ForegroundColor Cyan
Write-Host "   - Guía completa: INSTRUCCIONES_API_KEYS.md" -ForegroundColor White
Write-Host "   - Configuración: config/docker.secrets.yaml" -ForegroundColor White
Write-Host ""
Write-Host "🆘 SOPORTE:" -ForegroundColor Cyan
Write-Host "   - Logs: docker logs homepage" -ForegroundColor White
Write-Host "   - Reiniciar: docker restart homepage" -ForegroundColor White
Write-Host ""

# Volver al directorio original
Set-Location $currentPath

Write-Host "Presiona cualquier tecla para continuar..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
