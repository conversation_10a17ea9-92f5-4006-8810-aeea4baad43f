# widgets.yaml - Configuración completa de widgets para Homepage
# Incluye widgets funcionales para todos los servicios del stack Docker

- "Gestión y Red":
    - adguard:
        type: adguard
        url: http://tankeguard.duckdns.org
        username: {{HOMEPAGE_VAR_ADGUARD_USERNAME}}
        password: {{HOMEPAGE_VAR_ADGUARD_PASSWORD}}
    - portainer:
        type: portainer
        url: http://host.docker.internal:9000
        env: 2
        key: {{HOMEPAGE_VAR_PORTAINER_API_KEY}}

- "Descargas":
    - qbittorrent:
        type: qbittorrent
        url: http://tanketorrent.duckdns.org
        username: {{HOMEPAGE_VAR_QBITTORRENT_USERNAME}}
        password: {{HOMEPAGE_VAR_QBITTORRENT_PASSWORD}}
    - jackett:
        type: jackett
        url: http://tankejackett.duckdns.org
        key: {{HOMEPAGE_VAR_JACKETT_API_KEY}}

- "Automatización Multimedia":
    - sonarr:
        type: sonarr
        url: http://tankesonarr.duckdns.org
        key: {{HOMEPAGE_VAR_SONARR_API_KEY}}
    - radarr:
        type: radarr
        url: http://tankeradarr.duckdns.org
        key: {{HOMEPAGE_VAR_RADARR_API_KEY}}

- "Centro Multimedia":
    - jellyfin:
        type: jellyfin
        url: http://tankeflix.duckdns.org
        key: {{HOMEPAGE_VAR_JELLYFIN_API_KEY}}
    - jellyseerr:
        type: jellyseerr
        url: http://tankejellyseerr.duckdns.org
        key: {{HOMEPAGE_VAR_JELLYSEERR_API_KEY}}

- "Recursos del Sistema":
    - cpu:
        type: cpu
    - memory:
        type: memory
    - disk_c:
        type: disk
        path: /mnt/c
        label: "Disco C:"
    - disk_d:
        type: disk
        path: /mnt/d
        label: "Disco D:"
    - disk_e:
        type: disk
        path: /mnt/e
        label: "Disco E:"
    - disk_f:
        type: disk
        path: /mnt/f
        label: "Disco F:"