[2025-07-29 00:00:00.000 +02:00] [INF] [16] Jellyfin.Plugin.PlaybackReporting.TaskCleanDb: Playback Reporting Data Trim
[2025-07-29 00:00:00.000 +02:00] [INF] [16] Jellyfin.Plugin.PlaybackReporting.TaskCleanDb: MaxDataAge : 3
[2025-07-29 00:00:00.000 +02:00] [INF] [16] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: DeleteOldData : "delete from PlaybackActivity where DateCreated < '2025-04-29 00:00:00.0005325'"
[2025-07-29 00:00:00.004 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Playback Reporting Trim Db" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:00:00.117 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:00:00.117 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:00:00.117 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 00:00:01.000 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Playback Reporting Trim Db" set to fire at 2025-07-30 00:00:00.000 +02:00, which is 23:59:58.9995877 from now.
[2025-07-29 00:00:18.324 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:00:38.344 +02:00] [INF] [16] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:00:58.377 +02:00] [INF] [16] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:01:14.090 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:01:14.600 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:01:18.413 +02:00] [INF] [16] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:01:38.478 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:01:58.500 +02:00] [INF] [16] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:02:18.558 +02:00] [INF] [16] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:02:38.577 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:02:45.091 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:02:45.598 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:02:58.602 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:03:18.644 +02:00] [INF] [16] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:03:38.687 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:03:58.717 +02:00] [INF] [16] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:04:01.259 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:04:01.259 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:04:01.259 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 00:04:16.087 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:04:16.596 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:04:18.779 +02:00] [INF] [16] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:04:38.828 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:04:58.893 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:05:00.038 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:05:00.038 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:05:00.038 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 00:05:18.933 +02:00] [INF] [16] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:05:38.984 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:05:47.086 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:05:47.595 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:05:59.039 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:06:19.098 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:06:39.166 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:06:59.200 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:07:18.083 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:07:18.591 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:07:19.273 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:07:39.310 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:07:59.366 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:08:19.420 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:08:39.456 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:08:49.082 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:08:49.590 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:08:59.535 +02:00] [INF] [16] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:09:19.561 +02:00] [INF] [16] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:09:39.601 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:09:59.652 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:10:00.040 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:10:00.040 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:10:00.040 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 00:10:19.707 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:10:20.076 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:10:20.585 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:10:39.780 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:11:01.816 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:11:31.895 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:11:51.074 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:11:51.586 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:11:52.299 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:12:12.321 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:12:32.350 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:12:52.408 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:13:12.439 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:13:22.073 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:13:22.583 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:13:32.480 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:13:52.511 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:14:12.569 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:14:32.604 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:14:52.657 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:14:53.068 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:14:53.585 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:15:00.041 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:15:00.041 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:15:00.041 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 00:15:12.712 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:15:32.729 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:15:52.776 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:16:12.812 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:16:24.070 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:16:24.590 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:16:32.849 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:16:53.647 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:17:14.516 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:17:34.560 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:17:54.589 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:17:55.068 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:17:55.590 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:18:14.640 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:18:34.686 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:18:54.720 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:19:14.753 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:19:26.065 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:19:26.584 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:19:34.793 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:19:54.826 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:20:00.022 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:20:00.022 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:20:00.023 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 00:20:14.867 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:20:34.915 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:20:54.949 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:20:57.064 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:20:57.582 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:21:14.989 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:21:35.032 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:21:55.075 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:22:15.130 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:22:28.064 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:22:28.580 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:22:35.185 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:22:55.197 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:23:15.285 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:23:36.276 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:23:56.344 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:23:59.060 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:23:59.575 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:24:16.372 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:24:36.399 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:24:56.445 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:25:00.035 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:25:00.035 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:25:00.035 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 00:25:16.475 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:25:30.061 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:25:30.577 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:25:36.569 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:25:56.600 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:26:16.641 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:26:36.708 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:26:56.771 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:27:01.055 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:27:01.571 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:27:16.883 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:27:36.891 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:27:56.917 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:28:16.969 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:28:32.052 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:28:32.571 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:28:37.029 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:28:57.096 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:29:17.123 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:29:37.197 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:29:57.236 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:30:00.034 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:30:00.034 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:30:00.034 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 00:30:03.051 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:30:03.566 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:30:17.294 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:30:37.351 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:30:57.429 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:31:17.451 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:31:34.047 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:31:34.563 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:31:37.525 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:31:57.593 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:32:17.621 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:32:37.689 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:32:57.717 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:33:05.048 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:33:05.564 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:33:17.781 +02:00] [INF] [16] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:33:37.849 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:33:57.921 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:34:17.947 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:34:36.050 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:34:36.557 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:34:38.015 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:34:58.070 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:35:00.036 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:35:00.036 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:35:00.036 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 00:35:18.093 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:35:38.200 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:35:58.227 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:36:07.044 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:36:07.555 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:36:18.280 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:36:38.342 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:36:58.404 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:37:18.430 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:37:38.041 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:37:38.490 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:37:38.549 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:37:58.555 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:38:18.601 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:38:38.672 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:38:58.721 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:39:09.039 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:39:09.550 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:39:19.127 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:39:39.177 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:39:59.207 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:40:00.049 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:40:00.049 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:40:00.049 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 00:40:19.237 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:40:39.288 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:40:40.035 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:40:40.547 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:40:59.324 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:41:19.389 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:41:39.408 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:41:59.450 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:42:11.035 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:42:11.543 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:42:19.480 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:42:39.520 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:42:58.985 +02:00] [INF] [14] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.11" playing "Niños perdidos". Stopped at "3422458" ms
[2025-07-29 00:42:59.003 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Playback stop tracker found, processing stop : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-5265c98aa97d3832e69479a0cf131e62"
[2025-07-29 00:42:59.003 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.Data.PlaybackTracker: PlaybackTracker : Adding Stop Event : 07/29/2025 00:42:59
[2025-07-29 00:42:59.003 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Saving playback tracking activity in DB
[2025-07-29 00:43:06.548 +02:00] [INF] [19] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-07-29 00:43:06.705 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Adding playback tracker : 31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-6a52c649ccae51b7366598f9e9072e34
[2025-07-29 00:43:06.705 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.Data.PlaybackTracker: PlaybackTracker : Adding Start Event : 07/29/2025 00:43:06
[2025-07-29 00:43:06.705 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Creating StartPlaybackTimer Task
[2025-07-29 00:43:06.705 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: StartPlaybackTimer : Entered
[2025-07-29 00:43:07.644 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-6a52c649ccae51b7366598f9e9072e34"
[2025-07-29 00:43:09.885 +02:00] [INF] [3] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.11" playing "Ojalá estuvieras aquí". Stopped at "1909" ms
[2025-07-29 00:43:09.900 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Playback stop tracker found, processing stop : "31a21a712f203d9bc4c144070b732ee774a256ad-a8d66ec8890940eaaa34a5a7c5e10f69-6a52c649ccae51b7366598f9e9072e34"
[2025-07-29 00:43:09.900 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.Data.PlaybackTracker: PlaybackTracker : Adding Stop Event : 07/29/2025 00:43:09
[2025-07-29 00:43:09.900 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Playback stop but TrackedPlaybackInfo not found! not storing activity in DB
[2025-07-29 00:43:26.705 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: session.RemoteEndPoint : "172.19.0.1"
[2025-07-29 00:43:26.706 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: StartPlaybackTimer : event_playing_id     = "6a52c649ccae51b7366598f9e9072e34"
[2025-07-29 00:43:26.706 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: StartPlaybackTimer : event_user_id        = "a8d66ec8890940eaaa34a5a7c5e10f69"
[2025-07-29 00:43:26.706 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: StartPlaybackTimer : event_user_id_int    = 1
[2025-07-29 00:43:26.706 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: StartPlaybackTimer : session_playing_id   = ""
[2025-07-29 00:43:26.706 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: StartPlaybackTimer : session_user_id      = "a8d66ec8890940eaaa34a5a7c5e10f69"
[2025-07-29 00:43:26.706 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: StartPlaybackTimer : play_method          = "na"
[2025-07-29 00:43:26.706 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: StartPlaybackTimer : e.ClientName         = "Android TV"
[2025-07-29 00:43:26.706 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: StartPlaybackTimer : e.DeviceName         = "Agustin's Fire TV"
[2025-07-29 00:43:26.706 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: StartPlaybackTimer : ItemName             = "Dexter - s04e11 - Ojalá estuvieras aquí"
[2025-07-29 00:43:26.706 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: StartPlaybackTimer : ItemId               = "6a52c649ccae51b7366598f9e9072e34"
[2025-07-29 00:43:26.706 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: StartPlaybackTimer : ItemType             = "Episode"
[2025-07-29 00:43:26.706 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: StartPlaybackTimer : Details do not match for play item
[2025-07-29 00:43:26.706 +02:00] [INF] [14] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: StartPlaybackTimer : Exited
[2025-07-29 00:43:42.033 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:43:42.542 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:45:00.026 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:45:00.027 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:45:00.027 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 00:45:13.029 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:45:13.543 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:46:44.027 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:46:44.540 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:48:15.024 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:48:15.539 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:49:46.020 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:49:46.535 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:50:00.047 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:50:00.047 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:50:00.048 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 00:51:17.019 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:51:17.535 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:52:48.016 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:52:48.532 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:54:19.010 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:54:19.529 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:55:00.036 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:55:00.036 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 00:55:00.036 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 00:55:50.011 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:55:50.527 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:57:21.007 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:57:21.522 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:58:52.006 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 00:58:52.522 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:00:00.046 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:00:00.046 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:00:00.046 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 01:00:23.010 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:00:23.518 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:01:54.007 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:01:54.515 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:03:25.005 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:03:25.511 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:04:56.002 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:04:56.513 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:05:00.054 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:05:00.054 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:05:00.054 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 01:06:27.000 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:06:27.511 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:07:57.997 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:07:58.512 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:09:28.992 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:09:29.510 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:10:00.050 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:10:00.050 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:10:00.050 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 01:10:59.990 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:11:00.508 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:12:30.989 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:12:31.508 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:14:01.987 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:14:02.506 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:15:00.039 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:15:00.039 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:15:00.039 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 01:15:32.984 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:15:33.502 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:17:03.978 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:17:04.501 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:18:34.981 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:18:35.496 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:20:00.029 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:20:00.029 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:20:00.029 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 01:20:05.979 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:20:06.494 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:21:36.975 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:21:37.490 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:23:07.972 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:23:08.493 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:24:38.974 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:24:39.487 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:25:00.041 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:25:00.041 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:25:00.041 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 01:26:09.972 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:26:10.486 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:27:40.972 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:27:41.487 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:29:11.971 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:29:12.484 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:30:00.047 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:30:00.047 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:30:00.049 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 01:30:42.969 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:30:43.483 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:32:13.965 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:32:14.481 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:33:44.961 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:33:45.482 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:35:00.033 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:35:00.033 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:35:00.033 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 01:35:15.959 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:35:16.482 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:36:46.957 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:36:47.481 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:38:17.956 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:38:18.481 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:39:48.953 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:39:49.477 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:40:00.028 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:40:00.028 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:40:00.028 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 01:41:19.954 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:41:20.473 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:42:50.953 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:42:51.472 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:44:21.953 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:44:22.472 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:45:00.040 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:45:00.040 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:45:00.040 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 01:45:52.950 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:45:53.469 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:47:23.948 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:47:24.470 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:48:54.945 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:48:55.468 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:50:00.036 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:50:00.036 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:50:00.036 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 01:50:25.942 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:50:26.465 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:51:56.939 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:51:57.465 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:53:27.936 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:53:28.463 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:54:58.938 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:54:59.460 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:55:00.034 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:55:00.034 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 01:55:00.035 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 01:56:29.933 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:56:30.458 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:58:00.934 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:58:01.461 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:59:31.932 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:59:32.462 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:59:56.421 +02:00] [INF] [10] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-07-29 01:59:56.516 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-07-29 01:59:57.273 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-29 02:00:00.000 +02:00, which is 00:00:02.7262285 from now.
[2025-07-29 02:00:00.022 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:00:00.023 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:00:00.023 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 02:00:00.130 +02:00] [INF] [10] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-07-29 02:00:00.230 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:00:01.005 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-07-30 02:00:00.000 +02:00, which is 23:59:58.9942568 from now.
[2025-07-29 02:01:02.931 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:01:03.462 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:02:33.924 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:02:34.456 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:04:04.920 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:04:05.453 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:05:00.034 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:05:00.034 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:05:00.034 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 02:05:35.918 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:05:36.450 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:07:06.915 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:07:07.449 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:08:37.912 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:08:38.448 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:10:00.037 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:10:00.037 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:10:00.037 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 02:10:08.913 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:10:09.448 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:11:39.909 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:11:40.449 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:13:10.911 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:13:11.450 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:14:41.909 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:14:42.451 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:15:00.042 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:15:00.042 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:15:00.043 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 02:16:12.905 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:16:13.451 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:17:43.905 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:17:44.452 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:19:14.904 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:19:15.447 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:20:00.047 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:20:00.047 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:20:00.047 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 02:20:45.899 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:20:46.445 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:22:16.903 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:22:17.444 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:23:47.903 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:23:48.441 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:25:00.016 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:25:00.017 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:25:00.017 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 02:25:18.901 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:25:19.445 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:26:49.897 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:26:50.440 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:28:20.900 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:28:21.438 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:29:51.895 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:29:52.435 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:30:00.052 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:30:00.052 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:30:00.052 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 02:31:22.894 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:31:23.433 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:32:53.894 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:32:54.433 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:34:24.890 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:34:25.425 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:35:00.042 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:35:00.043 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:35:00.043 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 02:35:55.887 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:35:56.423 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:37:26.884 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:37:27.425 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:38:57.883 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:38:58.424 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:40:00.032 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:40:00.032 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:40:00.033 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 02:40:28.884 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:40:29.423 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:41:59.879 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:42:00.421 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:43:30.877 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:43:31.421 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:45:00.048 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:45:00.048 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:45:00.048 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 02:45:01.876 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:45:02.420 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:46:32.876 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:46:33.419 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:48:03.873 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:48:04.416 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:49:34.869 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:49:35.413 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:50:00.066 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:50:00.066 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:50:00.066 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 02:51:05.870 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:51:06.413 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:52:36.870 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:52:37.409 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:54:07.868 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:54:08.410 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:55:00.028 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:55:00.028 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 02:55:00.028 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 02:55:38.866 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:55:39.406 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:57:09.868 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:57:10.408 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:58:40.864 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:58:41.410 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:59:56.300 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-07-29 02:59:57.279 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-29 03:00:00.000 +02:00, which is 00:00:02.7210587 from now.
[2025-07-29 03:00:00.036 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:00:00.037 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:00:00.038 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:00:00.038 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 03:00:00.042 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:00:00.115 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:00:00.115 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 03:00:01.003 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-07-30 03:00:00.000 +02:00, which is 23:59:58.9967834 from now.
[2025-07-29 03:00:11.862 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:00:12.406 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:01:42.862 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:01:43.405 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:03:13.859 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:03:14.405 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:04:44.862 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:04:45.403 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:05:00.039 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:05:00.039 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:05:00.039 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 03:06:15.859 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:06:16.397 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:07:46.857 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:07:47.397 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:09:17.856 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:09:18.395 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:10:00.019 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:10:00.019 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:10:00.019 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 03:10:48.856 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:10:49.394 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:12:19.854 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:12:20.394 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:13:50.853 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:13:51.391 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:15:00.036 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:15:00.036 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:15:00.036 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 03:15:21.849 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:15:22.388 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:16:52.850 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:16:53.387 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:18:23.845 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:18:24.384 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:19:54.845 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:19:55.384 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:20:00.030 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:20:00.030 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:20:00.030 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 03:21:25.845 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:21:26.380 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:22:56.841 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:22:57.377 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:24:27.838 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:24:28.373 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:25:00.033 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:25:00.033 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:25:00.033 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 03:25:58.835 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:25:59.373 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:27:29.836 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:27:30.376 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:29:00.831 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:29:01.373 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:30:00.042 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:30:00.043 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:30:00.043 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 03:30:31.832 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:30:32.374 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:32:02.831 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:32:03.373 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:33:33.828 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:33:34.371 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:35:00.061 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:35:00.061 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:35:00.061 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 03:35:04.823 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:35:05.372 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:36:35.823 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:36:36.370 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:38:06.818 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:38:07.365 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:39:37.817 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:39:38.364 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:40:00.034 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:40:00.034 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:40:00.034 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 03:41:08.813 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:41:09.361 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:42:39.809 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:42:40.362 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:44:10.810 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:44:11.353 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:45:00.033 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:45:00.033 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:45:00.033 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 03:45:41.808 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:45:42.354 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:47:12.811 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:47:13.353 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:48:43.805 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:48:44.351 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:50:00.033 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:50:00.033 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:50:00.033 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 03:50:14.801 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:50:15.351 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:51:45.801 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:51:46.348 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:53:16.801 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:53:17.346 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:54:47.797 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:54:48.345 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:55:00.050 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:55:00.050 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 03:55:00.051 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 03:56:18.794 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:56:19.342 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:57:49.792 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:57:50.338 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:59:20.789 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 03:59:21.337 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:00:00.040 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:00:00.040 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:00:00.040 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 04:00:51.785 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:00:52.332 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:02:22.784 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:02:23.331 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:03:53.777 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:03:54.324 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:05:00.031 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:05:00.031 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:05:00.032 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 04:05:24.774 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:05:25.326 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:06:55.767 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:06:56.322 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:08:26.764 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:08:27.321 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:09:57.758 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:09:58.320 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:10:00.027 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:10:00.027 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:10:00.027 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 04:11:28.757 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:11:29.315 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:12:59.754 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:13:00.312 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:14:30.749 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:14:31.305 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:15:00.041 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:15:00.042 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:15:00.042 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 04:16:01.746 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:16:02.305 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:17:32.743 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:17:33.299 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:19:03.739 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:19:04.298 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:20:00.035 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:20:00.036 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:20:00.036 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 04:20:34.736 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:20:35.292 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:22:05.732 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:22:06.288 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:23:36.729 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:23:37.289 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:25:00.030 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:25:00.031 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:25:00.031 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 04:25:07.728 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:25:08.288 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:26:38.727 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:26:39.285 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:28:09.725 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:28:10.283 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:29:40.718 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:29:41.280 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:30:00.037 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:30:00.037 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:30:00.037 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 04:31:11.716 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:31:12.277 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:32:42.712 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:32:43.278 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:34:13.710 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:34:14.272 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:35:00.021 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:35:00.021 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:35:00.021 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 04:35:44.708 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:35:45.267 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:37:15.709 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:37:16.264 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:38:46.709 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:38:47.262 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:40:00.036 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:40:00.036 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:40:00.036 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 04:40:17.708 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:40:18.266 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:41:48.703 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:41:49.266 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:43:19.700 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:43:20.266 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:44:50.698 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:44:51.265 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:45:00.035 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:45:00.035 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:45:00.035 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 04:46:21.699 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:46:22.264 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:47:52.698 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:47:53.255 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:49:23.700 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:49:24.252 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:50:00.031 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:50:00.031 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:50:00.031 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 04:50:54.698 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:50:55.254 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:52:25.697 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:52:26.256 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:53:56.690 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:53:57.257 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:55:00.094 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:55:00.095 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 04:55:00.095 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 04:55:27.689 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:55:28.256 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:56:58.688 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:56:59.256 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:58:29.682 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 04:58:30.250 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:00:00.052 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:00:00.053 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:00:00.053 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:00:00.056 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:00:00.056 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 05:00:00.057 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 05:00:00.679 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:00:01.253 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:01:31.680 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:01:32.249 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:03:02.680 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:03:03.241 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:04:33.681 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:04:34.242 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:05:00.055 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:05:00.055 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:05:00.055 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 05:06:04.677 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:06:05.237 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:07:35.675 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:07:36.237 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:09:06.673 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:09:07.234 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:10:00.012 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:10:00.012 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:10:00.012 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 05:10:37.667 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:10:38.229 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:12:08.669 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:12:09.224 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:13:39.670 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:13:40.226 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:15:00.084 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:15:00.088 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:15:00.088 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 05:15:10.664 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:15:11.220 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:16:41.659 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:16:42.216 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:18:12.655 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:18:13.210 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:19:43.654 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:19:44.208 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:20:00.039 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:20:00.040 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:20:00.040 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 05:21:14.649 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:21:15.207 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:22:45.650 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:22:46.204 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:24:16.645 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:24:17.202 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:25:00.060 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:25:00.060 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:25:00.061 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 05:25:47.645 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:25:48.203 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:27:18.645 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:27:19.201 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:28:49.642 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:28:50.198 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:30:00.032 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:30:00.032 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:30:00.032 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 05:30:20.638 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:30:21.194 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:31:51.635 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:31:52.193 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:33:22.633 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:33:23.191 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:34:53.633 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:34:54.187 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:35:00.057 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:35:00.057 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:35:00.057 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 05:36:24.632 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:36:25.188 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:37:55.629 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:37:56.188 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:39:26.624 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:39:27.189 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:40:00.047 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:40:00.047 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:40:00.048 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 05:40:57.627 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:40:58.184 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:42:28.628 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:42:29.181 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:43:59.625 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:44:00.182 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:45:00.021 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:45:00.021 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:45:00.021 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 05:45:30.621 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:45:31.182 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:47:01.623 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:47:02.178 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:48:32.621 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:48:33.184 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:50:00.033 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:50:00.033 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:50:00.033 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 05:50:03.618 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:50:04.181 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:51:34.616 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:51:35.175 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:53:05.611 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:53:06.173 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:54:36.612 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:54:37.168 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:55:00.054 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:55:00.054 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 05:55:00.054 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 05:56:07.613 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:56:08.168 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:57:38.608 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:57:39.168 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:59:09.602 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 05:59:10.164 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:00:00.037 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:00:00.037 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:00:00.038 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 06:00:40.600 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:00:41.161 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:02:11.596 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:02:12.156 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:03:42.590 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:03:43.149 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:05:00.079 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:05:00.080 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:05:00.080 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 06:05:13.588 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:05:14.148 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:06:44.588 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:06:45.151 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:08:15.583 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:08:16.147 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:09:46.582 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:09:47.143 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:10:00.023 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:10:00.023 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:10:00.023 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 06:11:17.584 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:11:18.141 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:12:48.582 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:12:49.137 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:14:19.579 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:14:20.137 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:15:00.058 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:15:00.059 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:15:00.059 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 06:15:50.574 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:15:51.131 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:17:21.571 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:17:22.129 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:18:52.571 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:18:53.126 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:20:00.041 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:20:00.041 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:20:00.041 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 06:20:23.555 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:20:24.115 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:21:54.570 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:21:55.122 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:23:25.564 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:23:26.120 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:24:56.563 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:24:57.118 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:25:00.056 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:25:00.057 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:25:00.057 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 06:26:27.565 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:26:28.115 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:27:58.565 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:27:59.115 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:29:29.560 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:29:30.112 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:30:00.022 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:30:00.022 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:30:00.022 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 06:31:00.557 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:31:01.110 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:32:31.562 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:32:32.109 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:34:02.557 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:34:03.113 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:35:00.058 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:35:00.058 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:35:00.059 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 06:35:33.553 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:35:34.107 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:37:04.549 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:37:05.110 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:38:35.546 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:38:36.106 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:40:00.031 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:40:00.032 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:40:00.033 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 06:40:06.543 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:40:07.105 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:41:37.544 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:41:38.102 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:43:08.541 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:43:09.105 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:44:39.534 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:44:40.098 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:45:00.038 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:45:00.038 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:45:00.038 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 06:46:10.535 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:46:11.096 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:47:41.530 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:47:42.097 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:49:12.525 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:49:13.094 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:50:00.030 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:50:00.031 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:50:00.031 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 06:50:43.522 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:50:44.090 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:52:14.521 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:52:15.088 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:53:45.522 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:53:46.085 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:55:00.067 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:55:00.067 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 06:55:00.068 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 06:55:16.521 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:55:17.082 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:56:47.518 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:56:48.085 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:58:18.516 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:58:19.085 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:59:49.514 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 06:59:50.081 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:00:00.034 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:00:00.034 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:00:00.034 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 07:01:20.513 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:01:21.081 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:02:51.510 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:02:52.078 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:04:22.514 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:04:23.076 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:05:00.070 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:05:00.070 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:05:00.070 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 07:05:53.513 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:05:54.078 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:07:24.508 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:07:25.073 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:08:55.510 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:08:56.069 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:10:00.029 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:10:00.029 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:10:00.029 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 07:10:26.507 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:10:27.067 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:11:57.503 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:11:58.067 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:13:28.504 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:13:29.067 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:14:59.498 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:15:00.037 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:15:00.037 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:15:00.037 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 07:15:00.064 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:16:30.497 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:16:31.060 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:18:01.490 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:18:02.057 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:19:32.492 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:19:33.054 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:20:00.030 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:20:00.031 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:20:00.031 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 07:21:03.487 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:21:04.049 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:22:34.483 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:22:35.046 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:24:05.485 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:24:06.044 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:25:00.069 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:25:00.070 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:25:00.073 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 07:25:36.484 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:25:37.043 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:27:07.479 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:27:08.042 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:28:38.474 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:28:39.047 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:30:00.029 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:30:00.029 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:30:00.029 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 07:30:09.473 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:30:10.041 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:31:40.470 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:31:41.039 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:33:11.468 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:33:12.036 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:34:42.465 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:34:43.037 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:35:00.053 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:35:00.053 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:35:00.053 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 07:36:13.466 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:36:14.035 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:37:44.462 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:37:45.034 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:39:15.464 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:39:16.033 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:40:00.019 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:40:00.020 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:40:00.020 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 07:40:46.457 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:40:47.026 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:42:17.456 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:42:18.023 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:43:48.458 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:43:49.023 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:45:00.055 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:45:00.055 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:45:00.055 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 07:45:19.463 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:45:20.021 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:46:50.461 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:46:51.020 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:48:21.457 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:48:22.019 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:49:52.456 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:49:53.016 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:50:00.021 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:50:00.021 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:50:00.021 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 07:51:23.454 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:51:24.016 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:52:54.452 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:52:55.012 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:54:25.452 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:54:26.010 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:55:00.039 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:55:00.040 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 07:55:00.040 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 07:55:56.450 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:55:57.008 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:57:27.452 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:57:28.004 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:58:58.448 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 07:58:59.001 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:00:00.028 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:00:00.028 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:00:00.028 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 08:00:29.447 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:00:30.001 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:02:00.447 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:02:01.001 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:03:31.441 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:03:32.001 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:05:00.052 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:05:00.052 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:05:00.052 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 08:05:02.439 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:05:02.999 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:06:33.437 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:06:33.998 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:08:04.434 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:08:04.993 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:09:35.434 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:09:35.993 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:10:00.035 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:10:00.035 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:10:00.035 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 08:11:06.432 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:11:06.989 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:12:37.435 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:12:37.989 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:14:08.432 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:14:08.986 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:15:00.052 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:15:00.052 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:15:00.053 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 08:15:39.428 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:15:39.983 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:17:10.423 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:17:10.979 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:18:41.418 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:18:41.974 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:20:00.014 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:20:00.014 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:20:00.014 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 08:20:12.418 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:20:12.976 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:21:43.415 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:21:43.973 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:23:14.416 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:23:14.969 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:24:45.415 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:24:45.971 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:25:00.047 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:25:00.047 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:25:00.047 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 08:26:16.410 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:26:16.967 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:27:47.411 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:27:47.967 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:29:18.406 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:29:18.962 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:30:00.024 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:30:00.024 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:30:00.024 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 08:30:49.406 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:30:49.962 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:32:20.406 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:32:20.962 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:33:51.403 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:33:51.959 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:35:00.056 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:35:00.056 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:35:00.056 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 08:35:22.399 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:35:22.955 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:36:53.394 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:36:53.950 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:38:24.395 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:38:24.949 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:39:55.391 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:39:55.947 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:40:00.030 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:40:00.030 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:40:00.030 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 08:41:26.386 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:41:26.942 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:42:57.381 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:42:57.933 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:44:28.382 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:44:28.933 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:45:00.031 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:45:00.031 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:45:00.032 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 08:45:59.381 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:45:59.930 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:47:30.376 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:47:30.927 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:49:01.380 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:49:01.924 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:50:00.028 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:50:00.028 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:50:00.028 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 08:50:32.375 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:50:32.923 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:52:03.376 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:52:03.922 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:53:34.370 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:53:34.918 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:55:00.071 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:55:00.071 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 08:55:00.072 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 08:55:05.364 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:55:05.912 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:56:36.364 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:56:36.913 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:58:07.365 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:58:07.913 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:59:38.366 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 08:59:38.911 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:00:00.037 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:00:00.038 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:00:00.038 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 09:01:09.363 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:01:09.911 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:02:40.357 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:02:40.904 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:04:11.355 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:04:11.907 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:05:00.062 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:05:00.062 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:05:00.062 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 09:05:42.360 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:05:42.905 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:07:13.354 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:07:13.904 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:08:44.356 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:08:44.901 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:10:00.042 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:10:00.043 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:10:00.044 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 09:10:15.355 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:10:15.897 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:11:46.352 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:11:46.898 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:13:17.348 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:13:17.893 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:14:48.347 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:14:48.890 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:15:00.080 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:15:00.080 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:15:00.080 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 09:16:19.345 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:16:19.888 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:17:50.343 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:17:50.887 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:19:21.342 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:19:21.887 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:20:00.026 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:20:00.026 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:20:00.026 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 09:20:52.339 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:20:52.884 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:22:23.337 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:22:23.883 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:23:54.335 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:23:54.879 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:25:00.052 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:25:00.053 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:25:00.053 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 09:25:25.335 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:25:25.877 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:26:56.331 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:26:56.876 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:28:27.331 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:28:27.876 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:29:58.327 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:29:58.873 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:30:00.031 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:30:00.032 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:30:00.032 +02:00] [INF] [16] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 09:31:29.326 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:31:29.867 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:33:00.327 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:33:00.872 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:34:31.323 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:34:31.872 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:35:00.149 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:35:00.149 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:35:00.150 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 09:36:02.317 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:36:02.867 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:37:33.316 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:37:33.865 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:39:04.313 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:39:04.860 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:40:00.052 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:40:00.052 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:40:00.052 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 09:40:35.312 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:40:35.860 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:42:06.307 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:42:06.856 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:43:37.305 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:43:37.853 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:45:00.031 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:45:00.031 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:45:00.031 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 09:45:08.301 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:45:08.848 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:46:39.304 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:46:39.857 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:48:10.300 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:48:10.852 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:49:41.297 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:49:41.849 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:50:00.030 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:50:00.030 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:50:00.030 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 09:51:12.298 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:51:12.852 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:52:43.296 +02:00] [INF] [16] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:52:43.849 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:54:08.371 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-07-29 09:54:08.379 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-07-29 09:54:08.396 +02:00] [INF] [10] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-07-29 09:54:08.771 +02:00] [WRN] [10] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-29 09:54:09.153 +02:00] [WRN] [10] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-07-29 09:54:11.643 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escaneo de segmentos de medios" Completed after 0 minute(s) and 3 seconds
[2025-07-29 09:54:14.297 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:54:14.850 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:54:32.113 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 0 minute(s) and 23 seconds
[2025-07-29 09:54:32.327 +02:00] [INF] [14] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-07-29 09:54:32.507 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-07-29 09:55:00.039 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:55:00.039 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 09:55:00.039 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 09:55:45.295 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:55:45.845 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:57:16.293 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:57:16.843 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:58:47.291 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 09:58:47.841 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:00:00.080 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:00:00.083 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:00:00.084 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 10:00:18.294 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:00:18.839 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:01:49.290 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:01:49.842 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:03:20.294 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:03:20.840 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:04:51.290 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:04:51.838 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:05:00.061 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:05:00.061 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:05:00.061 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 10:06:22.292 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:06:22.835 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:07:53.290 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:07:53.832 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:09:24.290 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:09:24.837 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:10:00.035 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:10:00.035 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:10:00.035 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 10:10:55.283 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:10:55.831 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:12:26.283 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:12:26.830 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:13:57.279 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:13:57.828 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:15:00.069 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:15:00.070 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:15:00.070 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 10:15:28.279 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:15:28.827 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:16:59.279 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:16:59.828 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:18:30.284 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:18:30.824 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:20:00.031 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:20:00.031 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:20:00.032 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 10:20:01.280 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:20:01.819 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:21:32.277 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:21:32.817 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:23:03.273 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:23:04.077 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:24:34.271 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:24:35.074 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:25:00.066 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:25:00.067 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:25:00.067 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 10:26:05.265 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:26:06.074 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:27:36.260 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:27:37.068 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:29:07.258 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:29:08.063 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:30:00.027 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:30:00.027 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:30:00.027 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 10:30:38.252 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:30:39.062 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:32:09.248 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:32:10.059 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:33:40.247 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:33:41.055 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:35:00.069 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:35:00.069 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:35:00.070 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 10:35:11.244 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:35:12.051 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:36:42.240 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:36:43.082 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:38:13.237 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:38:14.080 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:39:44.230 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:39:45.074 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:40:00.053 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:40:00.055 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:40:00.065 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 10:41:15.229 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:41:16.073 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:42:46.222 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:42:47.069 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:44:17.222 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:44:18.066 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:45:00.085 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:45:00.086 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:45:00.088 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 10:45:48.222 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:45:49.074 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:47:19.217 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:47:20.068 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:48:50.211 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:48:51.065 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:50:00.091 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:50:00.092 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:50:00.095 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 10:50:21.206 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:50:22.065 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:51:52.204 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:51:53.063 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:53:23.204 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:53:24.057 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:54:54.205 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:54:55.061 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:55:00.059 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:55:00.059 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 10:55:00.059 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 10:56:25.203 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:56:26.057 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:57:56.198 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:57:57.053 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:59:27.193 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 10:59:28.054 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:00:00.053 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 11:00:00.054 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 11:00:00.054 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 11:00:58.193 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:00:59.055 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:02:29.189 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:02:30.053 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:04:00.190 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:04:01.052 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:05:00.081 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 11:05:00.081 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 11:05:00.082 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 11:05:31.183 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:05:32.048 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:07:02.180 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:07:03.044 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:08:33.180 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:08:34.043 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:10:00.060 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 11:10:00.060 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 11:10:00.060 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 11:10:04.175 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:10:05.039 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:11:35.170 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:11:36.036 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:13:06.170 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:13:07.035 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:14:37.171 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:14:38.035 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:15:00.079 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 11:15:00.079 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 11:15:00.079 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 11:16:08.165 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:16:09.034 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:17:39.163 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:17:40.030 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:19:10.161 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:19:11.030 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:20:00.028 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 11:20:00.028 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 11:20:00.029 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 11:20:41.161 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:20:42.027 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:22:12.157 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:22:13.022 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:23:43.155 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:23:44.020 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:25:00.158 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 11:25:00.159 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 11:25:00.162 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 11:25:14.153 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:25:15.017 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:26:45.147 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:26:46.010 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:28:16.148 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:28:17.010 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:29:47.143 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:29:48.013 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:30:00.039 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 11:30:00.040 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 11:30:00.040 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 11:31:18.146 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:31:19.005 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:32:49.146 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:32:50.006 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:34:20.141 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:34:21.001 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:35:00.073 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 11:35:00.073 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-07-29 11:35:00.073 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-07-29 11:35:51.141 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:35:52.003 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:37:22.139 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:37:22.998 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:38:53.135 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:38:53.995 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-07-29 11:39:33.129 +02:00] [INF] [17] Emby.Server.Implementations.Session.SessionManager: Sending shutdown notifications
