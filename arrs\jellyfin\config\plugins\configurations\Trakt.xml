<?xml version="1.0" encoding="utf-8"?>
<PluginConfiguration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <TraktUsers>
    <TraktUser>
      <AccessToken>304fab03830741587f91ed7be553bd6f23a3a040e85b132bf2b0b9735ee69cb2</AccessToken>
      <RefreshToken>b7689eb81b870b818efe98cea268c8b0a2f13ecba2004295cdbe7caf375b669f</RefreshToken>
      <LinkedMbUserId>a8d66ec8-8909-40ea-aa34-a5a7c5e10f69</LinkedMbUserId>
      <SkipUnwatchedImportFromTrakt>true</SkipUnwatchedImportFromTrakt>
      <SkipPlaybackProgressImportFromTrakt>false</SkipPlaybackProgressImportFromTrakt>
      <SkipWatchedImportFromTrakt>false</SkipWatchedImportFromTrakt>
      <PostWatchedHistory>true</PostWatchedHistory>
      <PostUnwatchedHistory>false</PostUnwatchedHistory>
      <PostSetWatched>true</PostSetWatched>
      <PostSetUnwatched>false</PostSetUnwatched>
      <ExtraLogging>false</ExtraLogging>
      <ExportMediaInfo>true</ExportMediaInfo>
      <SynchronizeCollections>true</SynchronizeCollections>
      <Scrobble>true</Scrobble>
      <LocationsExcluded />
      <AccessTokenExpiration>2025-07-29T08:17:28.3237304+02:00</AccessTokenExpiration>
      <DontRemoveItemFromTrakt>true</DontRemoveItemFromTrakt>
    </TraktUser>
  </TraktUsers>
</PluginConfiguration>