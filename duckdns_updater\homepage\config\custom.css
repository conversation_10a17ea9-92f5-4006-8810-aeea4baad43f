/* custom.css - Estilos personalizados para Homepage */

/* Mejoras visuales para el dashboard */
.homepage {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Estilo para las tarjetas de servicios */
.service-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.service-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Mejoras para los widgets */
.widget {
  border-radius: 8px;
  backdrop-filter: blur(5px);
}

/* Estilo para los iconos de estado */
.status-indicator {
  border-radius: 50%;
  width: 8px;
  height: 8px;
  display: inline-block;
  margin-right: 6px;
}

.status-online {
  background-color: #10b981;
  box-shadow: 0 0 6px rgba(16, 185, 129, 0.6);
}

.status-offline {
  background-color: #ef4444;
  box-shadow: 0 0 6px rgba(239, 68, 68, 0.6);
}

.status-warning {
  background-color: #f59e0b;
  box-shadow: 0 0 6px rgba(245, 158, 11, 0.6);
}

/* Mejoras para la barra de búsqueda */
.search-bar {
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Animaciones suaves */
* {
  transition: all 0.2s ease-in-out;
}

/* Mejoras para el modo oscuro */
.dark {
  --bg-primary: rgba(15, 23, 42, 0.8);
  --bg-secondary: rgba(30, 41, 59, 0.8);
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --border-color: rgba(71, 85, 105, 0.3);
}

/* Estilo para las métricas de sistema */
.system-metric {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  padding: 12px;
}

/* Mejoras para los gráficos */
.chart-container {
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.2);
  padding: 16px;
}

/* Estilo para los badges de estado */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.success {
  background-color: rgba(16, 185, 129, 0.2);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-badge.error {
  background-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-badge.warning {
  background-color: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

/* Mejoras para la navegación */
.nav-item {
  border-radius: 8px;
  padding: 8px 12px;
  margin: 2px 0;
}

.nav-item:hover {
  background-color: rgba(59, 130, 246, 0.1);
  border-left: 3px solid #3b82f6;
}

/* Estilo para los tooltips */
.tooltip {
  background-color: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.875rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Mejoras responsivas */
@media (max-width: 768px) {
  .service-card {
    margin-bottom: 16px;
  }

  .widget {
    padding: 12px;
  }
}

/* Animación de carga */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Mejoras para los enlaces */
a {
  text-decoration: none;
  color: #3b82f6;
  transition: color 0.2s ease-in-out;
}

a:hover {
  color: #1d4ed8;
  text-decoration: underline;
}