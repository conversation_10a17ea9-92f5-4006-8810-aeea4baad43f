# settings.yaml - Configuración principal de Homepage
title: "🏠 Panel de Control Tankeeee2"
favicon: https://cdn.jsdelivr.net/gh/walkxcode/dashboard-icons/png/homepage.png
background:
  image: https://images.unsplash.com/photo-1506318137071-a8e063b4bec0?q=80&w=2693&auto=format&fit=crop
  blur: sm
  saturate: 50
  brightness: 50
  opacity: 50

theme: dark
color: slate

# Configuración de idioma y formato
language: es
dateFormat: DD/MM/YYYY
timeFormat: 24hour

# Configuración de diseño
layout:
  - "Gestión y Red":
      icon: fas fa-network-wired
      style: row
      columns: 2
  - "Descargas":
      icon: fas fa-cloud-download-alt
      style: row
      columns: 2
  - "Automatización Multimedia":
      icon: fas fa-robot
      style: row
      columns: 2
  - "Centro Multimedia":
      icon: fas fa-film
      style: row
      columns: 2
  - "Recursos del Sistema":
      icon: fas fa-server
      style: row
      columns: 4

# Configuración de widgets globales
headerStyle: boxed
hideVersion: true
hideErrors: false
showStats: true
statsOpen: false

# Configuración de Docker
providers:
  docker:
    socket: /var/run/docker.sock

# Configuración de logs
logpath: /app/config/logs
target: _self

# Configuración de quicklaunch
quicklaunch:
  searchDescriptions: true
  hideInternetSearch: false
  hideVisitURL: false