# 🏠 Homepage - Configuración Completa

## 📋 Resumen de la Configuración

Se ha generado una configuración completa y funcional para Homepage que incluye widgets para todos los servicios de tu stack Docker.

## 🎯 Servicios Configurados

### ✅ **Gestión y Red**
- **Portainer**: Widget con estadísticas de contenedores
- **AdGuard Home**: Widget con estadísticas de DNS y bloqueos
- **Watchtower**: Enlace directo para actualizaciones

### ✅ **Descargas**
- **qBittorrent**: Widget con estadísticas de descargas
- **Jackett**: Widget con estado de indexers

### ✅ **Automatización Multimedia**
- **Sonarr**: Widget con estadísticas de series
- **Radarr**: Widget con estadísticas de películas

### ✅ **Centro Multimedia**
- **Jellyfin**: Widget con información de biblioteca
- **Jellyseerr**: Widget con solicitudes pendientes

### ✅ **Recursos del Sistema**
- **CPU**: Monitoreo en tiempo real
- **Memoria**: Uso de RAM
- **Almacenamiento**: Estado de discos C, D, E, F

## 📁 Archivos de Configuración

### 1. **services.yaml**
- Configuración de todos los servicios con widgets funcionales
- URLs de dominios DuckDNS configuradas
- Iconos y descripciones personalizadas

### 2. **widgets.yaml**
- Widgets específicos para cada servicio
- Configuración de autenticación con variables de entorno
- Monitoreo de recursos del sistema

### 3. **docker.secrets.yaml**
- Variables de entorno seguras para API keys
- Credenciales de autenticación
- Configuración centralizada de secrets

### 4. **bookmarks.yaml**
- Búsquedas rápidas para administración
- Enlaces directos a servicios
- Búsquedas en bases de datos de medios

### 5. **settings.yaml**
- Configuración visual mejorada
- Tema oscuro personalizado
- Layout optimizado para tu stack

### 6. **docker.yaml**
- Integración con Docker socket
- Estadísticas de contenedores

### 7. **custom.css**
- Estilos personalizados
- Mejoras visuales
- Animaciones y efectos

## 🔑 API Keys y Credenciales

### ✅ **Ya Configuradas** (encontradas en tu sistema)
```yaml
HOMEPAGE_VAR_SONARR_API_KEY: "2f9da07e98744f4890c0960d15ead111"
HOMEPAGE_VAR_RADARR_API_KEY: "cfc0cde90b0f483eb4190dc634ca86f2"
HOMEPAGE_VAR_JELLYFIN_API_KEY: "6a794b57ff4d4d10bf31875612761d8e"
HOMEPAGE_VAR_JACKETT_API_KEY: "xb68zjzfmw3cnbbmufcxr9ou7kh8te37"
HOMEPAGE_VAR_JELLYSEERR_API_KEY: "MTc1Mjc2MDk0NTQ5MzIzMjNhMzVmLWRlOTktNDkxMi04Y2IyLTYzNDU4OTFiN2U5Yg=="
HOMEPAGE_VAR_ADGUARD_USERNAME: "Tankeeee2_GAMES"
```

### ❌ **Pendientes de Configurar**
- `HOMEPAGE_VAR_ADGUARD_PASSWORD`: Contraseña de AdGuard Home
- `HOMEPAGE_VAR_PORTAINER_API_KEY`: API Key de Portainer
- `HOMEPAGE_VAR_QBITTORRENT_USERNAME`: Usuario de qBittorrent
- `HOMEPAGE_VAR_QBITTORRENT_PASSWORD`: Contraseña de qBittorrent

## 🚀 Configuración Rápida

### Opción 1: Script Automático
```powershell
cd C:\docker\duckdns_updater\homepage
.\configurar_homepage.ps1
```

### Opción 2: Manual
1. Edita `config\docker.secrets.yaml`
2. Reemplaza los valores marcados con "TU_"
3. Reinicia Homepage: `docker restart homepage`

## 🔧 Características Implementadas

### 🎨 **Mejoras Visuales**
- Tema oscuro personalizado con fondo de imagen
- Animaciones suaves y efectos hover
- Layout optimizado para tu stack de servicios
- Iconos Font Awesome para cada categoría

### 📊 **Widgets Funcionales**
- **AdGuard Home**: Consultas DNS, bloqueos, filtros activos
- **Portainer**: Estado de contenedores, imágenes, volúmenes
- **qBittorrent**: Descargas activas, velocidad, ratio
- **Sonarr/Radarr**: Series/películas monitoreadas, próximos episodios
- **Jellyfin**: Estadísticas de biblioteca, usuarios activos
- **Jackett**: Indexers configurados, estado de conectividad

### 🔍 **Búsquedas Integradas**
- Búsqueda directa en servicios desde Homepage
- Enlaces rápidos a bases de datos de medios
- Accesos directos a administración

### 🖥️ **Monitoreo del Sistema**
- CPU, RAM y almacenamiento en tiempo real
- Integración con Docker para estadísticas de contenedores
- Widgets de recursos del sistema

## 🌐 **URLs de Acceso**

- **Homepage**: https://tankeeee2.duckdns.org
- **Portainer**: http://tankeportainer.duckdns.org
- **AdGuard**: http://tankeguard.duckdns.org
- **qBittorrent**: http://tanketorrent.duckdns.org
- **Sonarr**: http://tankesonarr.duckdns.org
- **Radarr**: http://tankeradarr.duckdns.org
- **Jellyfin**: http://tankeflix.duckdns.org
- **Jellyseerr**: http://tankejellyseerr.duckdns.org
- **Jackett**: http://tankejackett.duckdns.org

## 🆘 **Solución de Problemas**

### Si un widget no funciona:
```bash
# Verificar logs
docker logs homepage

# Verificar configuración
cat config/docker.secrets.yaml

# Reiniciar servicio
docker restart homepage
```

### Si hay errores de conexión:
1. Verifica que el servicio esté ejecutándose
2. Confirma que la API key sea correcta
3. Asegúrate de que la URL sea accesible

## 📚 **Documentación Adicional**

- **Guía de API Keys**: `INSTRUCCIONES_API_KEYS.md`
- **Script de configuración**: `configurar_homepage.ps1`
- **Configuración de servicios**: `config/services.yaml`
- **Variables de entorno**: `config/docker.secrets.yaml`

## ✅ **Estado de la Configuración**

- ✅ Homepage funcionando sin errores de validación de host
- ✅ Integración con Caddy proxy configurada
- ✅ Configuración automática de Docker mantenida
- ✅ Compatibilidad con stack existente
- ✅ Widgets base configurados
- ⏳ Pendiente: Configuración de credenciales faltantes

¡Tu Homepage está listo para usar! Solo necesitas completar las credenciales pendientes para que todos los widgets funcionen correctamente.
