{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:00:00.028Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: <PERSON><PERSON><PERSON> Recently Added Scan","timestamp":"2025-07-28T22:00:00.098Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a698e0ff-6e28-43d7-9c54-0ded660fe20f","timestamp":"2025-07-28T22:00:00.098Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T22:00:00.111Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T22:00:00.120Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T22:00:00.120Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:01:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:02:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:03:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:04:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:05:00.025Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T22:05:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0a0028c0-e5ec-4e40-8902-a435ec04a924","timestamp":"2025-07-28T22:05:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T22:05:00.032Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T22:05:00.040Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T22:05:00.040Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:06:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:07:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:08:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:09:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:10:00.027Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T22:10:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"80460bc9-2bd6-4760-9c0f-e356f06ade86","timestamp":"2025-07-28T22:10:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T22:10:00.034Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T22:10:00.042Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T22:10:00.043Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:11:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:12:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:13:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:14:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:15:00.027Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T22:15:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"969a0d31-7d72-4e48-ac33-b18560ca64c3","timestamp":"2025-07-28T22:15:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T22:15:00.036Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T22:15:00.042Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T22:15:00.043Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:16:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:17:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:18:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:19:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:20:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T22:20:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"cfc4e6f3-7e50-4ebf-b4a3-6bb6c373804a","timestamp":"2025-07-28T22:20:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T22:20:00.016Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T22:20:00.024Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T22:20:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:21:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:22:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:23:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:24:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:25:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T22:25:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f3370050-dfc2-4edf-9f59-d5c98e48e843","timestamp":"2025-07-28T22:25:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T22:25:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T22:25:00.037Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T22:25:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:26:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:27:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:28:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:29:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:30:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T22:30:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d6dce6e5-f319-4f00-baae-10e1e3eece75","timestamp":"2025-07-28T22:30:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T22:30:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T22:30:00.035Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T22:30:00.035Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:31:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:32:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:33:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:34:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:35:00.024Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T22:35:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"14be64cf-ed6a-482d-b4b4-d9aad2ddc625","timestamp":"2025-07-28T22:35:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T22:35:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T22:35:00.037Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T22:35:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:36:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:37:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:38:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:39:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:40:00.026Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T22:40:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"eae15984-97c2-4eb8-966d-a4dfb05e247e","timestamp":"2025-07-28T22:40:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T22:40:00.039Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T22:40:00.050Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T22:40:00.050Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:41:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:42:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:43:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:44:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:45:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T22:45:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a968826c-3f4c-4bb7-8c3a-274d8a074861","timestamp":"2025-07-28T22:45:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T22:45:00.023Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T22:45:00.028Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T22:45:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:46:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:47:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:48:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:49:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:50:00.025Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T22:50:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"3cd47688-aed8-436c-b46a-a88bc8530296","timestamp":"2025-07-28T22:50:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T22:50:00.039Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T22:50:00.049Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T22:50:00.050Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:51:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:52:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:53:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:54:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:55:00.025Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T22:55:00.028Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"5ed99c23-cee1-4135-85f5-67ebbe8012fb","timestamp":"2025-07-28T22:55:00.028Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T22:55:00.033Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T22:55:00.037Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T22:55:00.037Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:56:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:57:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:58:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T22:59:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:00:00.025Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T23:00:00.028Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6a425294-7ac8-46cc-a71e-63849b98a4ac","timestamp":"2025-07-28T23:00:00.028Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Download Sync Reset","timestamp":"2025-07-28T23:00:00.035Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T23:00:00.041Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T23:00:00.048Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T23:00:00.048Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:01:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:02:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:03:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:04:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:05:00.027Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T23:05:00.036Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"c8b41c69-574f-491e-87ed-cd872d65cf94","timestamp":"2025-07-28T23:05:00.037Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T23:05:00.049Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T23:05:00.057Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T23:05:00.058Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:06:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:07:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:08:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:09:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:10:00.028Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T23:10:00.032Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"1f876511-ed0c-4adf-8200-636bf9a8ec42","timestamp":"2025-07-28T23:10:00.032Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T23:10:00.040Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T23:10:00.052Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T23:10:00.053Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:11:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:12:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:13:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:14:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:15:00.026Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T23:15:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"95fc5b92-98bd-43c6-ba3d-9ab9941e13a6","timestamp":"2025-07-28T23:15:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T23:15:00.035Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T23:15:00.040Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T23:15:00.041Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:16:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:17:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:18:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:19:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:20:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T23:20:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"1348dd8d-f303-41d6-9e61-eec1379c8ed8","timestamp":"2025-07-28T23:20:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T23:20:00.027Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T23:20:00.030Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T23:20:00.030Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:21:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:22:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:23:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:24:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:25:00.028Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T23:25:00.032Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"926e693d-329a-4fe6-8512-4a48c8c25112","timestamp":"2025-07-28T23:25:00.032Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T23:25:00.038Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T23:25:00.042Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T23:25:00.043Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:26:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:27:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:28:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:29:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:30:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T23:30:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"c33965e7-b849-4442-bf0e-ab40564f6237","timestamp":"2025-07-28T23:30:00.006Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Process Blacklisted Tags","timestamp":"2025-07-28T23:30:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T23:30:00.036Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T23:30:00.051Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T23:30:00.051Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:31:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:32:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:33:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:34:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:35:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T23:35:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"2c9e9763-a44f-46b2-9a1b-d9bd1a804c98","timestamp":"2025-07-28T23:35:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T23:35:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T23:35:00.036Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T23:35:00.040Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:36:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:37:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:38:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:39:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:40:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T23:40:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"2562ebd3-da8f-44cd-8490-fd2e314a0758","timestamp":"2025-07-28T23:40:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T23:40:00.019Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T23:40:00.032Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T23:40:00.033Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:41:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:42:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:43:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:44:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:45:00.024Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T23:45:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"11a4a591-97ea-4dfb-99d5-ed10b8444006","timestamp":"2025-07-28T23:45:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T23:45:00.037Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T23:45:00.042Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T23:45:00.042Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:46:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:47:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:48:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:49:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:50:00.026Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T23:50:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"043d676c-f2c8-4626-ba6c-e4a2299967cf","timestamp":"2025-07-28T23:50:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T23:50:00.032Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T23:50:00.038Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T23:50:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:51:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:52:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:53:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:54:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:55:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-28T23:55:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"641904ee-9c79-4b1a-acb2-99f1c259cb8e","timestamp":"2025-07-28T23:55:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-28T23:55:00.029Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-28T23:55:00.037Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-28T23:55:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:56:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:57:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:58:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-28T23:59:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:00:00.000Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T00:00:00.003Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"c41a105e-9697-450b-953f-fd142eff5dce","timestamp":"2025-07-29T00:00:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T00:00:00.014Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T00:00:00.024Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T00:00:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:01:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:02:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:03:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:04:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:05:00.023Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T00:05:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0b37c41d-e641-4f7f-9b86-cf664c12db54","timestamp":"2025-07-29T00:05:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T00:05:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T00:05:00.035Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T00:05:00.035Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:06:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:07:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:08:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:09:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:10:00.024Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T00:10:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b522a57d-cdc3-4a9a-bbce-79d67b9b35aa","timestamp":"2025-07-29T00:10:00.028Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T00:10:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T00:10:00.039Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T00:10:00.040Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:11:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:12:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:13:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:14:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:15:00.023Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T00:15:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6d00fc98-99ed-4022-ba2e-8c23d397f638","timestamp":"2025-07-29T00:15:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T00:15:00.037Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T00:15:00.045Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T00:15:00.047Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:16:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:17:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:18:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:19:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:20:00.026Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T00:20:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"54c30aef-d4f3-4a2d-aa45-ed1fdde6c1d2","timestamp":"2025-07-29T00:20:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T00:20:00.038Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T00:20:00.049Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T00:20:00.049Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:21:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:22:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:23:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:24:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:25:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T00:25:00.003Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ff47ce9a-9715-47e6-bf4b-34383779133c","timestamp":"2025-07-29T00:25:00.003Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T00:25:00.013Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T00:25:00.019Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T00:25:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:26:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:27:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:28:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:29:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:30:00.029Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T00:30:00.035Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"42561db3-b547-4049-ba23-ecdeec7451d0","timestamp":"2025-07-29T00:30:00.035Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T00:30:00.044Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T00:30:00.054Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T00:30:00.054Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:31:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:32:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:33:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:34:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:35:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T00:35:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"01c336da-0dcb-4efe-a4d5-8f7c404dce24","timestamp":"2025-07-29T00:35:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T00:35:00.036Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T00:35:00.046Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T00:35:00.047Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:36:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:37:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:38:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:39:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:40:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T00:40:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"60bf7336-2dc2-432c-a039-b1a896fa8ca8","timestamp":"2025-07-29T00:40:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T00:40:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T00:40:00.034Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T00:40:00.034Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:41:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:42:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:43:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:44:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:45:00.025Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T00:45:00.033Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"077dbd35-26bc-4166-9341-31447b60d804","timestamp":"2025-07-29T00:45:00.033Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T00:45:00.043Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T00:45:00.053Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T00:45:00.053Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:46:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:47:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:48:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:49:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:50:00.030Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T00:50:00.036Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"69d6f4c0-cdbc-4b20-8c68-2a2203d52186","timestamp":"2025-07-29T00:50:00.037Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T00:50:00.057Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T00:50:00.068Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T00:50:00.070Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:51:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:52:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:53:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:54:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:55:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T00:55:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"dd08199a-3366-430d-9431-2607eb4d417f","timestamp":"2025-07-29T00:55:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T00:55:00.022Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T00:55:00.030Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T00:55:00.030Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:56:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:57:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:58:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T00:59:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:00:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T01:00:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"41d78237-bc06-4f59-b3fb-b54d44599922","timestamp":"2025-07-29T01:00:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Full Scan","timestamp":"2025-07-29T01:00:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ffd573c5-74ee-40db-bf3f-e274101c403b","timestamp":"2025-07-29T01:00:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T01:00:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process library: Películas","timestamp":"2025-07-29T01:00:00.033Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T01:00:00.040Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T01:00:00.041Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T01:00:00.116Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T01:00:00.119Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:01:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:02:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:03:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:04:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:05:00.025Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T01:05:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d9eb0b05-3a4d-473e-9134-677ed9a6cce1","timestamp":"2025-07-29T01:05:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T01:05:00.034Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T01:05:00.040Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T01:05:00.041Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:06:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:07:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:08:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:09:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:10:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T01:10:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"3684c673-1eb2-4186-bcfa-63fc750a394a","timestamp":"2025-07-29T01:10:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T01:10:00.015Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T01:10:00.021Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T01:10:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:11:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:12:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:13:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:14:00.030Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:15:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T01:15:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8fab494d-0fba-4cfd-9972-4c67c86c8036","timestamp":"2025-07-29T01:15:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T01:15:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T01:15:00.038Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T01:15:00.039Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:16:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:17:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:18:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:19:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:20:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T01:20:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9c41065d-2bc3-4fff-9591-e7953ec06ec8","timestamp":"2025-07-29T01:20:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T01:20:00.025Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T01:20:00.031Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T01:20:00.032Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:21:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:22:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:23:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:24:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:25:00.024Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T01:25:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"1dceedb1-867f-4604-887f-19a2bb110039","timestamp":"2025-07-29T01:25:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T01:25:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T01:25:00.034Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T01:25:00.034Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:26:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:27:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:28:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:29:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:30:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T01:30:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"5d8885ff-2086-4454-9019-1903f1676f47","timestamp":"2025-07-29T01:30:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T01:30:00.035Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T01:30:00.045Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T01:30:00.046Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:31:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:32:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:33:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:34:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:35:00.027Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T01:35:00.035Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"746769f9-0b04-4d50-ac80-e780d5531c69","timestamp":"2025-07-29T01:35:00.035Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T01:35:00.047Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T01:35:00.064Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T01:35:00.064Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:36:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:37:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:38:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:39:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:40:00.024Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T01:40:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d53700c4-2ba8-46cf-9ca2-7443d9fa77bf","timestamp":"2025-07-29T01:40:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T01:40:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T01:40:00.035Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T01:40:00.035Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:41:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:42:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:43:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:44:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:45:00.024Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T01:45:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9d7a493c-0a54-4602-8aad-84ab21c34eaa","timestamp":"2025-07-29T01:45:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T01:45:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T01:45:00.034Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T01:45:00.035Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:46:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:47:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:48:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:49:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:50:00.023Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T01:50:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"e3c2c0fc-b7a4-4c9b-9ce9-43dfa0772917","timestamp":"2025-07-29T01:50:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T01:50:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T01:50:00.035Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T01:50:00.035Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:51:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:52:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:53:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:54:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:55:00.024Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T01:55:00.032Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"3b529436-0ad9-45e7-bd7b-0043cab0ff06","timestamp":"2025-07-29T01:55:00.032Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T01:55:00.041Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T01:55:00.053Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T01:55:00.054Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:56:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:57:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:58:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T01:59:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:00:00.023Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T02:00:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"bc215ebd-dc9f-48fe-a2a0-12d11897fc2a","timestamp":"2025-07-29T02:00:00.026Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Radarr Scan","timestamp":"2025-07-29T02:00:00.030Z"}
{"label":"Radarr Scan","level":"info","message":"Scan starting","sessionId":"28c5faf3-ed79-488d-9f7f-815ec0c9870d","timestamp":"2025-07-29T02:00:00.031Z"}
{"label":"Radarr Scan","level":"info","message":"Beginning to process Radarr server: Radarr","timestamp":"2025-07-29T02:00:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T02:00:00.036Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T02:00:00.041Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T02:00:00.041Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Un lugar tranquilo: Día uno","timestamp":"2025-07-29T02:00:00.569Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Destino final: Lazos de sangre","timestamp":"2025-07-29T02:00:00.572Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Malditos bastardos","timestamp":"2025-07-29T02:00:00.583Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Ocean's 8","timestamp":"2025-07-29T02:00:00.589Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Ocean's Eleven. Hagan juego","timestamp":"2025-07-29T02:00:00.591Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Ocean's Thirteen","timestamp":"2025-07-29T02:00:00.592Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Ocean's Twelve","timestamp":"2025-07-29T02:00:00.593Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Oppenheimer","timestamp":"2025-07-29T02:00:00.594Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for El contable 2","timestamp":"2025-07-29T02:00:00.597Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Shutter Island","timestamp":"2025-07-29T02:00:00.597Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Las crónicas de Narnia: La travesía del viajero del alba","timestamp":"2025-07-29T02:00:00.597Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for El señor de los anillos: La comunidad del anillo","timestamp":"2025-07-29T02:00:00.598Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Until Dawn","timestamp":"2025-07-29T02:00:00.599Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Los pecadores","timestamp":"2025-07-29T02:00:00.599Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Las crónicas de Narnia: El príncipe Caspian","timestamp":"2025-07-29T02:00:00.600Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for El mundo perdido: Jurassic Park","timestamp":"2025-07-29T02:00:00.600Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Las crónicas de Narnia: El león, la bruja y el armario","timestamp":"2025-07-29T02:00:00.601Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Los odiosos ocho","timestamp":"2025-07-29T02:00:00.602Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Kung Fu Panda","timestamp":"2025-07-29T02:00:00.609Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Kung Fu Panda 2","timestamp":"2025-07-29T02:00:00.613Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Kung Fu Panda 3","timestamp":"2025-07-29T02:00:00.614Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Kung Fu Panda 4","timestamp":"2025-07-29T02:00:00.615Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Una película de Minecraft","timestamp":"2025-07-29T02:00:00.616Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Gru 4. Mi villano favorito","timestamp":"2025-07-29T02:00:00.624Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Interstellar","timestamp":"2025-07-29T02:00:00.631Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Capitán América: Brave New World","timestamp":"2025-07-29T02:00:00.632Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Prometheus","timestamp":"2025-07-29T02:00:00.633Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Destino final 3 exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.648Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Vengadores: Infinity War exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.649Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Cómo entrenar a tu dragón 3 exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.650Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Náufrago exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.650Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Cómo entrenar a tu dragón exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.650Z"}
{"label":"Radarr Scan","level":"info","message":"Media for John Wick 4 exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.650Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Jungla de cristal exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.651Z"}
{"label":"Radarr Scan","level":"info","message":"Media for American History X exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.651Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Los renglones torcidos de Dios exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.651Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Destino final 4 exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.651Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Django desencadenado exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.652Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Destino final exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.652Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Cómo entrenar a tu dragón exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.652Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Destino final 5 exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.652Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Jurassic Park (Parque Jurásico) exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.653Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Destino final 2 exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.653Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Babylon exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.653Z"}
{"label":"Radarr Scan","level":"info","message":"Media for El renacido exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.653Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Cómo entrenar a tu dragón 2 exists. Changes were detected and the title will be updated.","timestamp":"2025-07-29T02:00:00.654Z"}
{"label":"Radarr Scan","level":"info","message":"Radarr scan complete","timestamp":"2025-07-29T02:00:04.659Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:01:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:02:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:03:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:04:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:05:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T02:05:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ffcae18e-eaaf-43d7-84f2-1b9b6d85f94b","timestamp":"2025-07-29T02:05:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T02:05:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T02:05:00.032Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T02:05:00.033Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:06:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:07:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:08:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:09:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:10:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T02:10:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7e7a4da4-97bd-4907-af8a-ab4c1acef34c","timestamp":"2025-07-29T02:10:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T02:10:00.022Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T02:10:00.029Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T02:10:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:11:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:12:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:13:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:14:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:15:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T02:15:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"61cf7faf-e1d8-4b4e-b489-db533749c730","timestamp":"2025-07-29T02:15:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T02:15:00.027Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T02:15:00.044Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T02:15:00.044Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:16:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:17:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:18:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:19:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:20:00.024Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T02:20:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"c820bbc4-9e40-48f2-a568-f474f4bb2150","timestamp":"2025-07-29T02:20:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T02:20:00.032Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T02:20:00.036Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T02:20:00.036Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:21:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:22:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:23:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:24:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:25:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T02:25:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a81299e3-0775-4f0c-9da1-e3fdde50a5ee","timestamp":"2025-07-29T02:25:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T02:25:00.027Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T02:25:00.032Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T02:25:00.033Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:26:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:27:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:28:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:29:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:30:00.023Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T02:30:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a3925b54-2efd-40e1-bfe3-5ff294aaa36c","timestamp":"2025-07-29T02:30:00.026Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Sonarr Scan","timestamp":"2025-07-29T02:30:00.030Z"}
{"label":"Sonarr Scan","level":"info","message":"Scan starting","sessionId":"4044fc40-0a7e-4e32-93b7-f8761e6703f2","timestamp":"2025-07-29T02:30:00.030Z"}
{"label":"Sonarr Scan","level":"info","message":"Beginning to process Sonarr server: Sonarr","timestamp":"2025-07-29T02:30:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T02:30:00.034Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T02:30:00.038Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T02:30:00.038Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Dragon Ball","timestamp":"2025-07-29T02:30:00.885Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Dark","timestamp":"2025-07-29T02:30:00.957Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Squid Game","timestamp":"2025-07-29T02:30:00.975Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: House of the Dragon","timestamp":"2025-07-29T02:30:00.978Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Prison Break","timestamp":"2025-07-29T02:30:00.981Z"}
{"label":"Sonarr Scan","level":"debug","message":"Detected 1 new standard season(s) for Rick and Morty","timestamp":"2025-07-29T02:30:00.992Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Peaky Blinders","timestamp":"2025-07-29T02:30:00.996Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Stranger Things","timestamp":"2025-07-29T02:30:00.999Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Breaking Bad","timestamp":"2025-07-29T02:30:01.005Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: The Big Bang Theory","timestamp":"2025-07-29T02:30:01.083Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: The Boys","timestamp":"2025-07-29T02:30:01.089Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Game of Thrones","timestamp":"2025-07-29T02:30:01.115Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Better Call Saul","timestamp":"2025-07-29T02:30:01.118Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Friends","timestamp":"2025-07-29T02:30:01.119Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Outlander","timestamp":"2025-07-29T02:30:01.126Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Dexter","timestamp":"2025-07-29T02:30:01.128Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Rick and Morty","timestamp":"2025-07-29T02:30:01.130Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Black Mirror","timestamp":"2025-07-29T02:30:01.131Z"}
{"label":"Sonarr Scan","level":"info","message":"Sonarr scan complete","timestamp":"2025-07-29T02:30:05.132Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:31:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:32:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:33:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:34:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:35:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T02:35:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"72b987df-ea36-46a7-8a50-0f34289cbdee","timestamp":"2025-07-29T02:35:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T02:35:00.015Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T02:35:00.023Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T02:35:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:36:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:37:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:38:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:39:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:40:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T02:40:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9d8e72b8-0449-421b-a930-7a73d98c3514","timestamp":"2025-07-29T02:40:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T02:40:00.032Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T02:40:00.038Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T02:40:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:41:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:42:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:43:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:44:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:45:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T02:45:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"95b59a1d-e235-466c-80e4-9f380316facd","timestamp":"2025-07-29T02:45:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T02:45:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T02:45:00.036Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T02:45:00.036Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:46:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:47:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:48:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:49:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:50:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T02:50:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a0d57b9f-c81e-450e-9e14-08dadc471d07","timestamp":"2025-07-29T02:50:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T02:50:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T02:50:00.033Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T02:50:00.033Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:51:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:52:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:53:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:54:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:55:00.025Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T02:55:00.036Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"592a9b87-f238-4bd1-ab6c-f5d665557f37","timestamp":"2025-07-29T02:55:00.036Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T02:55:00.049Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T02:55:00.097Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T02:55:00.098Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:56:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:57:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:58:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T02:59:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:00:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T03:00:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"818397b5-38f7-4d3a-9a6f-8edfb97f97d2","timestamp":"2025-07-29T03:00:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Image Cache Cleanup","timestamp":"2025-07-29T03:00:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Media Availability Sync","timestamp":"2025-07-29T03:00:00.021Z"}
{"label":"Availability Sync","level":"info","message":"Starting availability sync...","timestamp":"2025-07-29T03:00:00.021Z"}
{"label":"Image Cache","level":"error","message":"ENOENT: no such file or directory, scandir '/app/config/cache/images/tmdb'","timestamp":"2025-07-29T03:00:00.023Z"}
{"label":"Image Cache","level":"info","message":"Cleared 0 stale image(s) from cache 'tmdb'","timestamp":"2025-07-29T03:00:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T03:00:00.029Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T03:00:00.060Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T03:00:00.062Z"}
{"error":"apiError","errorMessage":"INVALID_AUTH_TOKEN","label":"AvailabilitySync","level":"error","message":"Sync interrupted.","status":401,"timestamp":"2025-07-29T03:00:00.066Z"}
{"label":"Availability Sync","level":"info","message":"Availability sync complete.","timestamp":"2025-07-29T03:00:00.068Z"}
{"label":"Image Cache","level":"info","message":"Cleared 2 stale image(s) from cache 'avatar'","timestamp":"2025-07-29T03:00:00.113Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:01:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:02:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:03:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:04:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:05:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T03:05:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"61a796a9-a043-4657-b7fa-3da9a4c9fdef","timestamp":"2025-07-29T03:05:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T03:05:00.020Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T03:05:00.056Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T03:05:00.056Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:06:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:07:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:08:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:09:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:10:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T03:10:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"24120438-eef3-4fd1-b389-0202c632e812","timestamp":"2025-07-29T03:10:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T03:10:00.009Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T03:10:00.013Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T03:10:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:11:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:12:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:13:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:14:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:15:00.028Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T03:15:00.036Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d295b085-3770-4f2b-b91e-ea6e0daad8d1","timestamp":"2025-07-29T03:15:00.036Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T03:15:00.048Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T03:15:00.091Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T03:15:00.091Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:16:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:17:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:18:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:19:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:20:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T03:20:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"174e7d32-494b-4869-b08f-d846005be295","timestamp":"2025-07-29T03:20:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T03:20:00.033Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T03:20:00.041Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T03:20:00.041Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:21:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:22:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:23:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:24:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:25:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T03:25:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"3e99175c-b515-4f14-853b-035fac185703","timestamp":"2025-07-29T03:25:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T03:25:00.029Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T03:25:00.063Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T03:25:00.063Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:26:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:27:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:28:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:29:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:30:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T03:30:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"41b027d0-4b59-439f-9289-bdb887f8c1a1","timestamp":"2025-07-29T03:30:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T03:30:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T03:30:00.034Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T03:30:00.034Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:31:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:32:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:33:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:34:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:35:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T03:35:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"bd119385-aa68-49ed-88c3-8a1072b7c84a","timestamp":"2025-07-29T03:35:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T03:35:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T03:35:00.058Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T03:35:00.058Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:36:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:37:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:38:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:39:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:40:00.023Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T03:40:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"3b5f8fb2-813e-4467-b723-7005fead7ee6","timestamp":"2025-07-29T03:40:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T03:40:00.040Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T03:40:00.051Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T03:40:00.052Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:41:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:42:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:43:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:44:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:45:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T03:45:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8d85d6ac-a120-4034-92eb-89057c844740","timestamp":"2025-07-29T03:45:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T03:45:00.017Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T03:45:00.023Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T03:45:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:46:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:47:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:48:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:49:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:50:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T03:50:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"5771fbeb-0b6c-4d02-a8f6-4d490b3bd225","timestamp":"2025-07-29T03:50:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T03:50:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T03:50:00.035Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T03:50:00.035Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:51:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:52:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:53:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:54:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:55:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T03:55:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"549e691b-27f2-461b-9062-46483963d8a3","timestamp":"2025-07-29T03:55:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T03:55:00.027Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T03:55:00.058Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T03:55:00.059Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:56:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:57:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:58:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T03:59:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:00:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T04:00:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"16707b32-9315-45ef-b628-b60a1361a0f3","timestamp":"2025-07-29T04:00:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T04:00:00.033Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T04:00:00.040Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T04:00:00.040Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:01:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:02:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:03:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:04:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:05:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T04:05:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"15959796-0556-4d37-9052-b3407bc0e00f","timestamp":"2025-07-29T04:05:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T04:05:00.034Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T04:05:00.085Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T04:05:00.089Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:06:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:07:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:08:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:09:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:10:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T04:10:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"96cf5f17-b293-4dff-a5b1-03ef31c8df72","timestamp":"2025-07-29T04:10:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T04:10:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T04:10:00.026Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T04:10:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:11:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:12:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:13:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:14:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:15:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T04:15:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"029201c1-fc0c-4406-8771-6a3660ab97fa","timestamp":"2025-07-29T04:15:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T04:15:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T04:15:00.061Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T04:15:00.061Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:16:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:17:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:18:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:19:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:20:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T04:20:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f3167d02-8ccf-461e-bb5d-dd95c3edfcf1","timestamp":"2025-07-29T04:20:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T04:20:00.034Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T04:20:00.043Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T04:20:00.044Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:21:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:22:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:23:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:24:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:25:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T04:25:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9bc55d38-4e94-4b69-ba6a-110b51f87bae","timestamp":"2025-07-29T04:25:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T04:25:00.022Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T04:25:00.060Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T04:25:00.060Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:26:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:27:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:28:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:29:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:30:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T04:30:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"74ae9211-a670-4a4a-bdb6-4affe0a8ad8e","timestamp":"2025-07-29T04:30:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T04:30:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T04:30:00.023Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T04:30:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:31:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:32:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:33:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:34:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:35:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T04:35:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9ac60cc5-9097-4af4-968f-bdba40ce226e","timestamp":"2025-07-29T04:35:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T04:35:00.024Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T04:35:00.062Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T04:35:00.062Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:36:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:37:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:38:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:39:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:40:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T04:40:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"c31fc2da-bcec-407b-802d-0fba45e3169b","timestamp":"2025-07-29T04:40:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T04:40:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T04:40:00.036Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T04:40:00.036Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:41:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:42:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:43:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:44:00.000Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:45:00.006Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T04:45:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"e25af662-781a-4cd1-9eac-d5da9364d705","timestamp":"2025-07-29T04:45:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T04:45:00.027Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T04:45:00.040Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T04:45:00.040Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:46:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:47:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:48:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:49:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:50:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T04:50:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d3ee1b5b-f9ee-431d-8d2e-bf2e8203341b","timestamp":"2025-07-29T04:50:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T04:50:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T04:50:00.032Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T04:50:00.032Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:51:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:52:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:53:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:54:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:55:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T04:55:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"07b6dc64-aa93-4b4c-ba69-0bab016e4d88","timestamp":"2025-07-29T04:55:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T04:55:00.035Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T04:55:00.072Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T04:55:00.073Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:56:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:57:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:58:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T04:59:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:00:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T05:00:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"c7ba8818-dd56-4036-a82b-e67591546040","timestamp":"2025-07-29T05:00:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T05:00:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T05:00:00.036Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T05:00:00.037Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:01:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:02:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:03:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:04:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:05:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T05:05:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"5c1ce56d-b083-465d-ae71-3eda41bf65ae","timestamp":"2025-07-29T05:05:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T05:05:00.033Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T05:05:00.073Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T05:05:00.073Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:06:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:07:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:08:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:09:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:10:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T05:10:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7ac02fe6-d30f-433b-a4bc-17b7895e7aaf","timestamp":"2025-07-29T05:10:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T05:10:00.025Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T05:10:00.031Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T05:10:00.031Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:11:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:12:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:13:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:14:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:15:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T05:15:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"babbadba-e9ac-41a5-a39b-efae03fe4e1a","timestamp":"2025-07-29T05:15:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T05:15:00.013Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T05:15:00.038Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T05:15:00.039Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:16:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:17:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:18:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:19:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:20:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T05:20:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"34c7ec0a-a1c1-40b4-9ba5-b715c88bf3c6","timestamp":"2025-07-29T05:20:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T05:20:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T05:20:00.032Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T05:20:00.032Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:21:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:22:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:23:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:24:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:25:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T05:25:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"5656513c-4d7e-4765-87b0-a71b31368098","timestamp":"2025-07-29T05:25:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T05:25:00.032Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T05:25:00.074Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T05:25:00.078Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:26:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:27:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:28:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:29:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:30:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T05:30:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"29e7c952-8a5e-415a-9f71-c644bda534d8","timestamp":"2025-07-29T05:30:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T05:30:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T05:30:00.030Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T05:30:00.031Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:31:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:32:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:33:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:34:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:35:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T05:35:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0cfc3cbf-2d52-4d8a-96f6-619c6c63c356","timestamp":"2025-07-29T05:35:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T05:35:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T05:35:00.055Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T05:35:00.055Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:36:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:37:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:38:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:39:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:40:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T05:40:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a28d8bda-0169-4482-b4b7-98f9dbc2b03b","timestamp":"2025-07-29T05:40:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T05:40:00.016Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T05:40:00.021Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T05:40:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:41:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:42:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:43:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:44:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:45:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T05:45:00.033Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"37e9e230-8173-4d57-9c6e-6c0ef8c90f09","timestamp":"2025-07-29T05:45:00.033Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T05:45:00.049Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T05:45:00.058Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T05:45:00.058Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:46:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:47:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:48:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:49:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:50:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T05:50:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ed7a7667-d931-49ce-a4a9-894e917c60a5","timestamp":"2025-07-29T05:50:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T05:50:00.017Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T05:50:00.022Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T05:50:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:51:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:52:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:53:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:54:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:55:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T05:55:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d0a8fd4d-148d-480c-8bc7-af61261a614a","timestamp":"2025-07-29T05:55:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T05:55:00.012Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T05:55:00.043Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T05:55:00.044Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:56:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:57:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:58:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T05:59:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:00:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T06:00:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"4d3eca2c-7efd-493d-a371-a90b6a4acbd1","timestamp":"2025-07-29T06:00:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T06:00:00.025Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T06:00:00.029Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T06:00:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:01:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:02:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:03:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:04:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:05:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T06:05:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"07bfcae5-5147-477e-8b02-244edd9e6330","timestamp":"2025-07-29T06:05:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T06:05:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T06:05:00.055Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T06:05:00.055Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:06:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:07:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:08:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:09:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:10:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T06:10:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"963a5c33-9ad3-4045-a2f5-a09de29bc87b","timestamp":"2025-07-29T06:10:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T06:10:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T06:10:00.038Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T06:10:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:11:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:12:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:13:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:14:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:15:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T06:15:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8af1a7e0-b409-414a-b4e3-31c2a7c08978","timestamp":"2025-07-29T06:15:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T06:15:00.024Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T06:15:00.057Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T06:15:00.058Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:16:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:17:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:18:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:19:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:20:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T06:20:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"c711fac1-66c2-4a31-85f5-4e0db27207b7","timestamp":"2025-07-29T06:20:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T06:20:00.010Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T06:20:00.015Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T06:20:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:21:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:22:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:23:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:24:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:25:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T06:25:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"adb74853-8357-4cdf-8ddb-bfa9c3652511","timestamp":"2025-07-29T06:25:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T06:25:00.015Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T06:25:00.049Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T06:25:00.049Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:26:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:27:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:28:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:29:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:30:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T06:30:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6eda187c-5685-4364-a138-25fdbad65489","timestamp":"2025-07-29T06:30:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T06:30:00.021Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T06:30:00.025Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T06:30:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:31:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:32:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:33:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:34:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:35:00.006Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T06:35:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"cc3d766e-e1d3-45d8-85e0-249d7e7e1dc7","timestamp":"2025-07-29T06:35:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T06:35:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T06:35:00.060Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T06:35:00.067Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:36:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:37:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:38:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:39:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:40:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T06:40:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ed061201-8682-42e4-bbd0-51403d90195c","timestamp":"2025-07-29T06:40:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T06:40:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T06:40:00.032Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T06:40:00.032Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:41:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:42:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:43:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:44:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:45:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T06:45:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ad85be70-00ec-4252-9a32-e855e2b4d11a","timestamp":"2025-07-29T06:45:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T06:45:00.027Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T06:45:00.033Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T06:45:00.033Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:46:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:47:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:48:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:49:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:50:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T06:50:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"19218060-4024-4129-8796-2b1ce4db508a","timestamp":"2025-07-29T06:50:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T06:50:00.025Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T06:50:00.029Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T06:50:00.030Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:51:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:52:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:53:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:54:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:55:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T06:55:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7973971f-3daa-4a10-be18-c60a14eca626","timestamp":"2025-07-29T06:55:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T06:55:00.034Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T06:55:00.074Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T06:55:00.075Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:56:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:57:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:58:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T06:59:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:00:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T07:00:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"92871f17-2b1e-4a80-9377-a93002978fac","timestamp":"2025-07-29T07:00:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T07:00:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T07:00:00.040Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T07:00:00.041Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:01:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:02:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:03:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:04:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:05:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T07:05:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"74e3ef4f-28c2-4e37-ad05-195b557212d4","timestamp":"2025-07-29T07:05:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T07:05:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T07:05:00.064Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T07:05:00.064Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:06:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:07:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:08:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:09:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:10:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T07:10:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a097c4fa-55e6-4e5e-80fb-e1b2094369ec","timestamp":"2025-07-29T07:10:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T07:10:00.034Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T07:10:00.047Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T07:10:00.047Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:11:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:12:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:13:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:14:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:15:00.023Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T07:15:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"c01d3606-cb9f-49e9-9618-d66b18cce82d","timestamp":"2025-07-29T07:15:00.032Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T07:15:00.046Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T07:15:00.085Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T07:15:00.085Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:16:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:17:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:18:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:19:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:20:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T07:20:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"4abdaaf3-62f0-4e5d-ba5d-57f062cd28a4","timestamp":"2025-07-29T07:20:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T07:20:00.023Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T07:20:00.027Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T07:20:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:21:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:22:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:23:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:24:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:25:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T07:25:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"60731f2c-ec27-4fa8-832a-e393f1f36b83","timestamp":"2025-07-29T07:25:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T07:25:00.025Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T07:25:00.054Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T07:25:00.056Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:26:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:27:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:28:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:29:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:30:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T07:30:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"527c58d8-bd33-40fa-b219-8f077b734dc6","timestamp":"2025-07-29T07:30:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T07:30:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T07:30:00.035Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T07:30:00.035Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:31:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:32:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:33:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:34:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:35:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T07:35:00.043Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"bcd44310-5f5c-47af-853b-9ef1fd904fd3","timestamp":"2025-07-29T07:35:00.043Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T07:35:00.109Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T07:35:00.174Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T07:35:00.175Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:36:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:37:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:38:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:39:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:40:00.006Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T07:40:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"501adc41-9b40-4046-b645-21d2bcd248b5","timestamp":"2025-07-29T07:40:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T07:40:00.022Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T07:40:00.056Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T07:40:00.063Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:41:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:42:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:43:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:44:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:45:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T07:45:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b03c2afc-2284-4aa6-942e-5e9f802aa13f","timestamp":"2025-07-29T07:45:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T07:45:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T07:45:00.033Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T07:45:00.033Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:46:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:47:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:48:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:49:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:50:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T07:50:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"39ac0d4e-10b1-4b52-823f-63d5a8020a62","timestamp":"2025-07-29T07:50:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T07:50:00.027Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T07:50:00.031Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T07:50:00.031Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:51:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:52:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:53:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:54:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:55:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T07:55:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0d9abe41-8e26-48de-93a8-81ad728fea9f","timestamp":"2025-07-29T07:55:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T07:55:00.034Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T07:55:00.041Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T07:55:00.042Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:56:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:57:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:58:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T07:59:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:00:00.032Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T08:00:00.055Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"71a3b3d0-3265-4194-a26f-8dea9aef4c5e","timestamp":"2025-07-29T08:00:00.055Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T08:00:00.069Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T08:00:00.089Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T08:00:00.090Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:01:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:02:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:03:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:04:00.031Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:05:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T08:05:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0d63719b-2562-40d3-8c91-e059c719c1de","timestamp":"2025-07-29T08:05:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T08:05:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T08:05:00.062Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T08:05:00.064Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:06:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:07:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:08:00.180Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:09:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:10:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T08:10:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"89191b56-07f7-4ad5-9d0b-4d9f56dccea1","timestamp":"2025-07-29T08:10:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T08:10:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T08:10:00.036Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T08:10:00.037Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:11:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:12:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:13:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:14:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:15:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T08:15:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"234a0933-dd1c-4c61-88bf-3ab9ad569ed2","timestamp":"2025-07-29T08:15:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T08:15:00.034Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T08:15:00.072Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T08:15:00.073Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:16:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:17:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:18:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:19:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:20:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T08:20:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"99074dbb-93f4-4f5a-8014-127526c91c6e","timestamp":"2025-07-29T08:20:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T08:20:00.027Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T08:20:00.034Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T08:20:00.034Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:21:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:22:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:23:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:24:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:25:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T08:25:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d2c04256-fc1f-4081-9f77-65cb58ef92a2","timestamp":"2025-07-29T08:25:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T08:25:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T08:25:00.069Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T08:25:00.069Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:26:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:27:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:28:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:29:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:30:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T08:30:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0f756878-711f-4f07-aca5-e8b474ead1ea","timestamp":"2025-07-29T08:30:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T08:30:00.022Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T08:30:00.029Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T08:30:00.030Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:31:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:32:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:33:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:34:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:35:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T08:35:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"1b67d859-2331-42f9-a342-22e0d98fc0c2","timestamp":"2025-07-29T08:35:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T08:35:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T08:35:00.073Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T08:35:00.073Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:36:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:37:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:38:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:39:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:40:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T08:40:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7dce60af-4c3b-459f-8d39-ea8c9595ff02","timestamp":"2025-07-29T08:40:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T08:40:00.034Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T08:40:00.073Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T08:40:00.074Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:41:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:42:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:43:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:44:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:45:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T08:45:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0f250837-baf3-46db-afcc-664bbf65c76d","timestamp":"2025-07-29T08:45:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T08:45:00.066Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T08:45:00.091Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T08:45:00.092Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:46:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:47:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:48:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:49:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:50:00.023Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T08:50:00.037Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"085adcfa-4fd9-4ffd-967f-e94f0bd286ab","timestamp":"2025-07-29T08:50:00.037Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T08:50:00.058Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T08:50:00.108Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T08:50:00.108Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:51:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:52:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:53:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:54:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:55:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T08:55:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f2e043c4-e1a0-4ce8-8c1e-336f55931c6b","timestamp":"2025-07-29T08:55:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T08:55:00.027Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T08:55:00.061Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T08:55:00.061Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:56:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:57:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:58:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T08:59:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:00:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T09:00:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"941bfaca-e112-401e-858b-de49f4321aa5","timestamp":"2025-07-29T09:00:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T09:00:00.039Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T09:00:00.056Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T09:00:00.057Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:01:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:02:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:03:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:04:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:05:00.038Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T09:05:00.045Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b8615403-c176-4f13-92c0-b790f369c2f4","timestamp":"2025-07-29T09:05:00.046Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T09:05:00.052Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T09:05:00.083Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T09:05:00.083Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:06:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:07:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:08:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:09:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:10:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T09:10:00.033Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"28d767e9-c21d-480d-a26d-14115bf79cd7","timestamp":"2025-07-29T09:10:00.033Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T09:10:00.048Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T09:10:00.063Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T09:10:00.063Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:11:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:12:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:13:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:14:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:15:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T09:15:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"70b20acd-7568-4528-a95d-d1f260b3ecd5","timestamp":"2025-07-29T09:15:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T09:15:00.032Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T09:15:00.085Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T09:15:00.088Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:16:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:17:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:18:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:19:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:20:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T09:20:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8d012854-2086-4f0b-83d5-f738d3b18719","timestamp":"2025-07-29T09:20:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T09:20:00.025Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T09:20:00.029Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T09:20:00.030Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:21:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:22:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:23:00.126Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:24:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:25:00.025Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T09:25:00.038Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"79ae8132-5f0e-469b-b541-aff6b1d813c2","timestamp":"2025-07-29T09:25:00.039Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T09:25:00.071Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T09:25:00.170Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T09:25:00.171Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:26:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:27:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:28:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:29:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:30:00.000Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T09:30:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"37f54514-8721-4ff5-a9f2-460d8322e58c","timestamp":"2025-07-29T09:30:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T09:30:00.020Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T09:30:00.047Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T09:30:00.047Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:31:00.000Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:32:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:33:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:34:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:35:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T09:35:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"e34763ff-89a6-4d86-ba1f-b6faa6f1e2fa","timestamp":"2025-07-29T09:35:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T09:35:00.022Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T09:35:00.075Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T09:35:00.076Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:36:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:37:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:38:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:39:00.013Z"}
{"level":"info","message":"Commit Tag: $GIT_SHA","timestamp":"2025-07-29T09:40:23.713Z"}
{"level":"info","message":"Starting Jellyseerr version 2.7.2","timestamp":"2025-07-29T09:40:25.528Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0001_migrate_hostname.js'...","timestamp":"2025-07-29T09:40:30.726Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0002_migrate_apitokens.js'...","timestamp":"2025-07-29T09:40:30.749Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0003_emby_media_server_type.js'...","timestamp":"2025-07-29T09:40:30.766Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0004_migrate_region_setting.js'...","timestamp":"2025-07-29T09:40:30.803Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0005_migrate_network_settings.js'...","timestamp":"2025-07-29T09:40:30.815Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0006_remove_lunasea.js'...","timestamp":"2025-07-29T09:40:30.823Z"}
{"label":"Notifications","level":"info","message":"Registered notification agents","timestamp":"2025-07-29T09:40:30.843Z"}
{"label":"Jobs","level":"info","message":"Scheduled jobs loaded","timestamp":"2025-07-29T09:40:31.028Z"}
{"label":"Server","level":"info","message":"Server ready on port 5055","timestamp":"2025-07-29T09:40:31.378Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:41:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:42:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:43:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:44:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:45:00.028Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T09:45:00.039Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"1f81fffe-b16f-491f-8fc0-94bc9254c8c6","timestamp":"2025-07-29T09:45:00.040Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T09:45:00.074Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T09:45:00.143Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T09:45:00.144Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:46:00.032Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:47:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:48:00.036Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:49:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:50:00.005Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T09:50:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"97993d43-f162-4d34-af02-44e677add333","timestamp":"2025-07-29T09:50:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T09:50:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T09:50:00.042Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T09:50:00.044Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:51:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:52:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:53:00.129Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:54:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:55:00.071Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T09:55:00.092Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"60fa9cfb-19f8-4021-9a2f-41ac70963714","timestamp":"2025-07-29T09:55:00.096Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T09:55:00.276Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T09:55:00.550Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T09:55:00.556Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:56:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:57:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:58:00.065Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T09:59:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:00:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T10:00:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"e99e3171-c8de-4434-8076-8c3951caa8c5","timestamp":"2025-07-29T10:00:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T10:00:00.032Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T10:00:00.054Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T10:00:00.054Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:01:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:02:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:03:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:04:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:05:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T10:05:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9607fb74-33dc-4cf7-ab84-41d3f70dbb07","timestamp":"2025-07-29T10:05:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T10:05:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T10:05:00.112Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T10:05:00.119Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:06:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:07:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:08:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:09:00.034Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:10:00.028Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T10:10:00.057Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8e22a25d-f5bf-4418-9542-a4726bdab0c7","timestamp":"2025-07-29T10:10:00.067Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T10:10:00.095Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T10:10:00.136Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T10:10:00.143Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:11:00.042Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:12:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:13:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:14:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:15:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T10:15:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d14db1f8-736b-41c2-a34f-21ed5718845e","timestamp":"2025-07-29T10:15:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T10:15:00.171Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T10:15:00.428Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T10:15:00.429Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:16:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:17:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:18:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:19:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:20:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T10:20:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"62cd16e2-b4f8-4d8b-95cf-6d9c231b6f65","timestamp":"2025-07-29T10:20:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T10:20:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T10:20:00.093Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T10:20:00.094Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:21:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:22:00.031Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:23:00.157Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:24:00.031Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:25:00.041Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T10:25:00.064Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"2db0783d-7fab-4a72-828b-2d5b47a7b102","timestamp":"2025-07-29T10:25:00.064Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T10:25:00.092Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T10:25:00.191Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T10:25:00.195Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:26:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:27:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:28:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:29:00.030Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:30:00.031Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T10:30:00.033Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d70d8f78-6d41-42b6-8d6a-50aefce3636a","timestamp":"2025-07-29T10:30:00.042Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T10:30:00.051Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T10:30:00.151Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T10:30:00.151Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:31:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:32:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:33:00.048Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:34:00.062Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:35:00.024Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T10:35:00.041Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f8b2ef7e-868b-4e2b-8adc-9c5fee0b55dd","timestamp":"2025-07-29T10:35:00.042Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T10:35:00.118Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T10:35:00.199Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T10:35:00.200Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:36:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:37:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:38:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:39:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:40:00.043Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T10:40:00.066Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"368574b0-686c-4edf-87ca-4b607fffaaf2","timestamp":"2025-07-29T10:40:00.067Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T10:40:00.093Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T10:40:00.174Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T10:40:00.175Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:41:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:42:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:43:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:44:00.036Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:45:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T10:45:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"baf7979e-796e-4bb2-8291-65792cfbd186","timestamp":"2025-07-29T10:45:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T10:45:00.041Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T10:45:00.074Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T10:45:00.082Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:46:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:47:00.041Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:48:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:49:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:50:00.028Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T10:50:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7f4c059b-ba0d-4d1a-9489-605b5d08ba6e","timestamp":"2025-07-29T10:50:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T10:50:00.041Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T10:50:00.099Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T10:50:00.100Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:51:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:52:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:53:00.070Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:54:00.033Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:55:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T10:55:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"e20f7b3c-4b75-436c-9b00-0ec62497fee7","timestamp":"2025-07-29T10:55:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T10:55:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T10:55:00.063Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T10:55:00.065Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:56:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:57:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:58:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T10:59:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:00:00.029Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T11:00:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"50a9fc63-7344-470d-abb7-31e42b00aba9","timestamp":"2025-07-29T11:00:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T11:00:00.042Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T11:00:00.058Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T11:00:00.059Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:01:00.030Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:02:00.000Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:03:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:04:00.045Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:05:00.027Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T11:05:00.069Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"63a42470-d03a-4904-a616-1fc5876eaeaf","timestamp":"2025-07-29T11:05:00.080Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T11:05:00.621Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T11:05:01.270Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T11:05:01.272Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:06:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:07:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:08:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:09:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:10:00.040Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T11:10:00.050Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"58140f01-658e-42a9-b883-28e939ee4ac6","timestamp":"2025-07-29T11:10:00.050Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T11:10:00.070Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T11:10:00.093Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T11:10:00.097Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:11:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:12:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:13:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:14:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:15:00.025Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-29T11:15:00.034Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"60ef3e01-68ed-47e5-b5c9-c44dd0c519d7","timestamp":"2025-07-29T11:15:00.035Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-29T11:15:00.138Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-29T11:15:00.314Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-29T11:15:00.317Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:16:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:17:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:18:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-29T11:19:00.010Z"}
