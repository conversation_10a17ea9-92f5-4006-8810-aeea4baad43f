# docker.secrets.yaml - Variables de entorno seguras para Homepage
# IMPORTANTE: Reemplaza los valores marcados con "TU_" con tus credenciales reales

# === ADGUARD HOME ===
# Usuario y contraseña de AdGuard Home (configurados en la interfaz web)
HOMEPAGE_VAR_ADGUARD_USERNAME: "Tankeeee2_GAMES"
HOMEPAGE_VAR_ADGUARD_PASSWORD: "TU_CONTRASEÑA_ADGUARD"

# === PORTAINER ===
# API Key de Portainer (Configuración > Users > Access tokens)
HOMEPAGE_VAR_PORTAINER_API_KEY: "TU_API_KEY_DE_PORTAINER"

# === QBITTORRENT ===
# Usuario y contraseña de qBittorrent (configurados en la interfaz web)
HOMEPAGE_VAR_QBITTORRENT_USERNAME: "admin"
HOMEPAGE_VAR_QBITTORRENT_PASSWORD: "TU_CONTRASEÑA_QBITTORRENT"

# === JACKETT ===
# API Key de Jackett (visible en la página principal de Jackett)
HOMEPAGE_VAR_JACKETT_API_KEY: "xb68zjzfmw3cnbbmufcxr9ou7kh8te37"

# === SONARR ===
# API Key de Sonarr (Configuración > General > API Key)
HOMEPAGE_VAR_SONARR_API_KEY: "2f9da07e98744f4890c0960d15ead111"

# === RADARR ===
# API Key de Radarr (Configuración > General > API Key)
HOMEPAGE_VAR_RADARR_API_KEY: "cfc0cde90b0f483eb4190dc634ca86f2"

# === JELLYFIN ===
# API Key de Jellyfin (Panel de administración > API Keys)
HOMEPAGE_VAR_JELLYFIN_API_KEY: "6a794b57ff4d4d10bf31875612761d8e"

# === JELLYSEERR ===
# API Key de Jellyseerr (Configuración > General > API Key)
HOMEPAGE_VAR_JELLYSEERR_API_KEY: "MTc1Mjc2MDk0NTQ5MzIzMjNhMzVmLWRlOTktNDkxMi04Y2IyLTYzNDU4OTFiN2U5Yg=="