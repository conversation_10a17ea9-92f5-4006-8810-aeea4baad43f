# services.yaml - Configuración de servicios para Homepage
# Incluye todos los servicios del stack Docker con widgets funcionales

- "Gestión y Red":
    - Portainer:
        href: http://tankeportainer.duckdns.org
        description: Gestión de Contenedores Docker
        icon: portainer.png
        widget:
          type: portainer
          url: http://host.docker.internal:9000
          env: 2
          key: {{HOMEPAGE_VAR_PORTAINER_API_KEY}}

    - AdGuard Home:
        href: http://tankeguard.duckdns.org
        description: Bloqueador de Anuncios y DNS
        icon: adguard-home.png
        widget:
          type: adguard
          url: http://tankeguard.duckdns.org
          username: {{HOMEPAGE_VAR_ADGUARD_USERNAME}}
          password: {{HOMEPAGE_VAR_ADGUARD_PASSWORD}}

    - Watchtower:
        href: https://containrrr.dev/watchtower/
        description: Actualizador automático de contenedores
        icon: watchtower.png

- "Descargas":
    - qBittorrent:
        href: http://tanketorrent.duckdns.org
        description: Cliente BitTorrent
        icon: qbittorrent.png
        widget:
          type: qbittorrent
          url: http://tanketorrent.duckdns.org
          username: {{HOMEPAGE_VAR_QBITTORRENT_USERNAME}}
          password: {{HOMEPAGE_VAR_QBITTORRENT_PASSWORD}}

    - Jackett:
        href: http://tankejackett.duckdns.org
        description: Proxy de Indexers
        icon: jackett.png
        widget:
          type: jackett
          url: http://tankejackett.duckdns.org
          key: {{HOMEPAGE_VAR_JACKETT_API_KEY}}

- "Automatización Multimedia":
    - Sonarr:
        href: http://tankesonarr.duckdns.org
        description: Gestión de Series
        icon: sonarr.png
        widget:
          type: sonarr
          url: http://tankesonarr.duckdns.org
          key: {{HOMEPAGE_VAR_SONARR_API_KEY}}

    - Radarr:
        href: http://tankeradarr.duckdns.org
        description: Gestión de Películas
        icon: radarr.png
        widget:
          type: radarr
          url: http://tankeradarr.duckdns.org
          key: {{HOMEPAGE_VAR_RADARR_API_KEY}}

- "Centro Multimedia":
    - Jellyfin:
        href: http://tankeflix.duckdns.org
        description: Servidor de Medios
        icon: jellyfin.png
        widget:
          type: jellyfin
          url: http://tankeflix.duckdns.org
          key: {{HOMEPAGE_VAR_JELLYFIN_API_KEY}}

    - Jellyseerr:
        href: http://tankejellyseerr.duckdns.org
        description: Solicitudes de Contenido
        icon: jellyseerr.png
        widget:
          type: jellyseerr
          url: http://tankejellyseerr.duckdns.org
          key: {{HOMEPAGE_VAR_JELLYSEERR_API_KEY}}